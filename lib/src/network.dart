import 'dart:convert';

import 'package:dio/dio.dart';

class CurlInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final buffer = StringBuffer();
    buffer.write("curl -X ${options.method} '${options.uri}'");

    options.headers.forEach((key, value) {
      buffer.write(" -H '$key: $value'");
    });

    if (options.data != null) {
      if (options.data is Map) {
        final data = jsonEncode(options.data);
        buffer.write(" --data-raw '$data'");
      } else if (options.data is String) {
        buffer.write(" --data-raw '${options.data}'");
      }
    }

    print(buffer.toString()); // Вывод в лог cURL запроса

    super.onRequest(options, handler);
  }
}
