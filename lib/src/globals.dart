import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:terminal/models/counties_response.dart';
import 'package:terminal/models/create_order_model.dart';
import 'package:terminal/models/create_payment_model.dart';
import 'package:terminal/models/doc_type_response.dart';
import 'package:terminal/models/login_model.dart';
import 'package:terminal/models/stations_model.dart';
import '../models/config_model.dart';
import '../models/trip_order_model.dart';

enum DataLoadingState { NA, LOADING, LOADED, REFRESH, ERROR }

class Globals extends GetxService {
  final isLoggedIn = false.obs;
  final dataState = DataLoadingState.NA.obs;
  final terminalData = TerminalData().obs;
  final terminalConfig = Config().obs;
  final currentStationConfig = Config().obs;
  final terminalPopularStations = List<Station>.empty(growable: true).obs;
  final selectedStationFrom = Station().obs;
  final selectedStationTo = Station().obs;
  final selectedDate = DateTime.now().obs;
  TripOrder tripOrder = TripOrder();
  List<Country> countriesList = [];
  List<DocType> docTypeList = [];
  late SharedPreferences prefs;
  final createPayment = CreatePaymentModel().obs; //Информация о созданном платеже
  final returnTickets = <Ticket>[].obs; // Список билетов для возврата

  @override
  void onInit() async {
    super.onInit();
    await loadPrefs();

    // Слушаем изменения и сохраняем в prefs
    terminalData.listen((_) => savePrefs());
    terminalConfig.listen((_) => savePrefs());
    selectedStationFrom.listen((_) => savePrefs());
    selectedStationTo.listen((_) => savePrefs());
    selectedDate.listen((_) => savePrefs());
    returnTickets.listen((_) => savePrefs());

    terminalConfig.listen((data) {
      currentStationConfig.value = data;
      selectedStationFrom.value.title = currentStationConfig.value.defaultDispatch?.title ?? "";
    });
  }

  Future<void> resetAllData() async {
    //TODO: - Добавить необходимый сброс
    selectedStationTo.value = Station();
    returnTickets.value = [];
    createPayment.value = CreatePaymentModel();
  }

  Future<void> savePrefs() async {
    prefs = await SharedPreferences.getInstance();

    prefs.setString('terminalData', terminalData.value.toRawJson());
    prefs.setString('terminalConfig', terminalConfig.value.toRawJson());
    prefs.setString('selectedStationFrom', selectedStationFrom.value.toRawJson());
    prefs.setString('selectedStationTo', selectedStationTo.value.toRawJson());
    prefs.setInt('selectedDate', selectedDate.value.millisecondsSinceEpoch);

    final returnTicketsJson = jsonEncode(returnTickets.map((ticket) => ticket.toRawJson()).toList());
    prefs.setString('returnTickets', returnTicketsJson);
  }

  /// Удаляем все сохранённые данные
  Future<void> clearPrefs() async {
    prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  /// Загружаем данные из SharedPreferences
  Future<void> loadPrefs() async {
    prefs = await SharedPreferences.getInstance();

    final terminalDataJson = prefs.getString('terminalData') ?? "{}";
    terminalData.value = TerminalData.fromJson(json.decode(terminalDataJson));

    final terminalConfigJson = prefs.getString('terminalConfig') ?? "{}";
    terminalConfig.value = Config.fromJson(json.decode(terminalConfigJson));

    final selectedFromJson = prefs.getString('selectedStationFrom') ?? "{}";
    selectedStationFrom.value = Station.fromJson(json.decode(selectedFromJson));

    final selectedToJson = prefs.getString('selectedStationTo') ?? "{}";
    selectedStationTo.value = Station.fromJson(json.decode(selectedToJson));

    final selectedDateMillis = prefs.getInt('selectedDate') ?? DateTime.now().millisecondsSinceEpoch;
    selectedDate.value = DateTime.fromMillisecondsSinceEpoch(selectedDateMillis);

    // Проверяем состояние isLoggedIn
    isLoggedIn.value = terminalData.value.access != null && terminalData.value.host != null;

    // Загрузка returnTickets
    final returnTicketsJson = prefs.getString('returnTickets');
    if (returnTicketsJson != null) {
      final List<dynamic> decodedList = jsonDecode(returnTicketsJson);
      returnTickets.value = decodedList.map((item) => Ticket.fromJson(jsonDecode(item))).toList();
    }
  }
}
