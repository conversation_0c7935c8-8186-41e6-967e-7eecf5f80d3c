import 'dart:async';
import 'dart:convert';

import 'package:get/get.dart' hide Response;
import 'package:dio/dio.dart';
import 'package:talker/talker.dart';
import 'package:talker_dio_logger/talker_dio_logger.dart';
import 'package:terminal/models/api_error_model.dart';
import 'package:terminal/models/api_result_model.dart';
import 'package:terminal/models/create_order_model.dart';
import 'package:terminal/models/create_payment_model.dart';
import 'package:terminal/models/doc_type_response.dart';

import 'package:terminal/models/login_model.dart';
import 'package:punycode_converter/punycode_converter.dart';
import 'package:terminal/models/order_to_place.dart';
import 'package:terminal/models/print_command_model.dart';
import 'package:terminal/models/qrcreate_response.dart';
import 'package:terminal/models/qrstatus_response.dart';
import 'package:terminal/models/stations_model.dart';
import 'package:terminal/models/trip_detail_model.dart';
import 'package:terminal/src/network.dart';
import 'package:terminal/ui/helpers.dart';

import '../models/config_model.dart';
import '../models/counties_response.dart';
import '../models/trip_model.dart';
import 'globals.dart';

final talker = Talker();

class Api extends GetxService {
  final globals = Get.find<Globals>();
  late final Dio _dio;
  late Worker initUserData;

  // Формирование полного URL
  String _buildUrl(String endpoint) {
    return "https://${globals.terminalData.value.host}/app${endpoint}";
  }

  onInit() async {
    _dio = Dio(BaseOptions(
        // connectTimeout: Duration(seconds: 3),
        // receiveTimeout: Duration(seconds: 3),
        headers: {
          "Accept": "application/json",
          "Content-Type": "application/json",
        }));

    _dio.interceptors.add(InterceptorsWrapper(
      onError: (DioException err, ErrorInterceptorHandler handler) async {
        if (err.response?.statusCode == 401) {
          final res = await tokenRefresh();
          if (res.access != null) {
            globals.terminalData.value.access = res.access;

            final opts = Options(
              method: err.requestOptions.method,
              headers: {
                ...err.requestOptions.headers,
                'Authorization': 'Bearer ${res.access}',
              },
            );

            final cloneReq = await _dio.request(
              err.requestOptions.path,
              options: opts,
              data: err.requestOptions.data,
              queryParameters: err.requestOptions.queryParameters,
            );

            return handler.resolve(cloneReq);
          }
        }
        return handler.next(err);
      },
    ));

    _dio.interceptors.addAll([
      CurlInterceptor(), // Логирует cURL
      TalkerDioLogger(talker: talker),
    ]);

    initUserData = ever(globals.terminalData, (TerminalData data) async {
      if (data.access == null) return;
      if (globals.terminalData.value.host == null) return;

      _dio.options.headers["Authorization"] = "Bearer ${globals.terminalData.value.access}";

      final res = await getConfig();
      globals.terminalConfig.value = res;

      final res2 = await getPopularArrivalStationsById(globals.terminalConfig.value.defaultDispatch?.id ?? "");
      globals.terminalPopularStations.value = res2 ?? [];
    });

    globals.loadPrefs();
    super.onInit();
  }

  onClose() {
    initUserData.dispose();
    super.onClose();
  }

  /// GET-запрос
  Future<Response?> get(String endpoint, {Map<String, dynamic>? queryParameters, bool isShowError = true}) async {
    globals.dataState.value = DataLoadingState.LOADING;
    try {
      final response = await _dio.get(endpoint, queryParameters: queryParameters);
      globals.dataState.value = DataLoadingState.LOADED;
      return response;
    } on DioException catch (e) {
      if (isShowError) handleBadRequestError(e);
      globals.dataState.value = DataLoadingState.LOADED;
      return e.response;
    }
  }

  /// POST-запрос
  Future<Response?> post(String endpoint, {Map<String, dynamic>? data, bool isShowError = true}) async {
    globals.dataState.value = DataLoadingState.LOADING;
    try {
      final response = await _dio.post(endpoint, data: data);
      globals.dataState.value = DataLoadingState.LOADED;
      return response;
    } on DioException catch (e) {
      if (isShowError) handleBadRequestError(e);
      globals.dataState.value = DataLoadingState.LOADED;
      return e.response;
    }
  }

  /// DELETE-запрос
  Future<Response?> delete(String endpoint, {Map<String, dynamic>? data}) async {
    globals.dataState.value = DataLoadingState.LOADING;
    try {
      final response = await _dio.delete(endpoint, data: data);
      globals.dataState.value = DataLoadingState.LOADED;
      return response;
    } on DioException catch (e) {
      handleBadRequestError(e);
      globals.dataState.value = DataLoadingState.LOADED;
      return e.response;
    }
  }

  void handleBadRequestError(DioException error) {
    globals.dataState.value = DataLoadingState.ERROR;

    //if (error.response?.statusCode == 400) {
    final errorData = error.response?.data;
    if (errorData != null && errorData is Map<String, dynamic>) {
      final errorMessage = errorData['message'] ?? "Неизвестная ошибка";
      ErrorHandler.showError(errorMessage);
    }
    //}
  }

  Future<TerminalData> login(
    String host,
    String username,
    String password,
  ) async {
    final url = 'https://${Punycode.domainEncode(host)}/app/token/';
    final data = {
      'username': username,
      'password': password,
    };
    final response = await post(url, data: data, isShowError: false);
    if (response != null && response.statusCode == 200) {
      return TerminalData.fromRawJson(response.toString());
    } else {
      ErrorHandler.showError("Не удалось войти");
      return TerminalData.fromJson({"error": "Не удалось войти"});
    }
  }

  Future<TerminalData> tokenRefresh() async {
    final String url = _buildUrl("/token/refresh/");
    final data = {
      'refresh': globals.terminalData.value.refresh,
    };
    final response = await post(url, data: data);
    if (response != null && response.statusCode == 200) {
      return TerminalData.fromRawJson(response.toString());
    } else {
      return TerminalData.fromJson({"error": "Не удалось обновить токен"});
    }
  }

  Future<Config> getConfig() async {
    final url = _buildUrl("/config/");
    final response = await get(url);
    if (response != null && response.statusCode == 200) {
      return Config.fromRawJson(response.toString());
    } else {
      return Config.fromJson({"error": "Не удалось получить конфигурацию"});
    }
  }

  Future<List<Station>?> getPopularArrivalStationsById(String searchId) async {
    final url = _buildUrl("/arrival/popular/");
    final params = {'dispatch_id': searchId};
    final response = await get(url, queryParameters: params);
    if (response != null && response.statusCode == 200) {
      final arr = jsonDecode(jsonEncode(response.data));
      List<Station> stations = [];
      for (var a in arr) {
        if (a['title'] != "") stations.add(Station.fromJson(a));
      }
      return stations;
    } else {
      return null;
    }
  }

  Future<List<Station>?> getPopularArrivalStationsByName(String search) async {
    final url = _buildUrl("/arrival/popular/");
    final params = {'q': search};
    final response = await get(url, queryParameters: params);
    if (response != null && response.statusCode == 200) {
      final arr = jsonDecode(jsonEncode(response.data));
      List<Station> stations = [];
      for (var a in arr) {
        if (a['title'] != "") stations.add(Station.fromJson(a));
      }
      return stations;
    } else {
      return null;
    }
  }

  Future<List<Station>?> getPopularDispatchStationsByName(bool isPopular) async {
    final stringURL = isPopular ? "/dispatch/popular/" : "/dispatch/";
    final String url = _buildUrl(stringURL);
    final response = await get(url);
    if (response != null && response.statusCode == 200) {
      final arr = jsonDecode(jsonEncode(response.data));
      List<Station> stations = [];
      for (var a in arr) {
        if (a['title'] != "") stations.add(Station.fromJson(a));
      }
      return stations;
    } else {
      return null;
    }
  }

  Future<List<Station>?> getArrivalStationsByName(String search, String dispatchId) async {
    final String url = _buildUrl("/arrival/");
    final params = {
      'q': search,
      'dispatch_id': dispatchId,
    };
    final response = await get(url, queryParameters: params);
    if (response != null && response.statusCode == 200) {
      final arr = jsonDecode(jsonEncode(response.data));
      List<Station> stations = [];
      for (var a in arr) {
        if (a['title'] != "") stations.add(Station.fromJson(a));
      }
      return stations;
    } else {
      return null;
    }
  }

  Future<List<Station>?> getDispatchStationsByName(String search) async {
    final String url = _buildUrl("/dispatch/");
    final params = {'q': search};
    final response = await get(url, queryParameters: params);
    if (response != null && response.statusCode == 200) {
      final arr = jsonDecode(jsonEncode(response.data));
      List<Station> stations = [];
      for (var a in arr) {
        if (a['title'] != "") stations.add(Station.fromJson(a));
      }
      return stations;
    } else {
      return null;
    }
  }

  Future<Map<String, dynamic>?> getPrices(String fromId, String toId) async {
    final url = _buildUrl("/routes/calendar/");
    final params = {
      'dispatch_id': fromId,
      'arrival_id': toId,
    };
    final response = await get(url, queryParameters: params);
    if (response != null && response.statusCode == 200) {
      return jsonDecode(jsonEncode(response.data));
    } else {
      return {};
    }
  }

  Future<List<Trip>> getTrips(String fromId, String toId, DateTime date) async {
    final url = _buildUrl("/trips/");
    final params = {
      'dispatch_id': Uri.parse(fromId),
      'arrival_id': Uri.parse(toId),
      'trip_date': "${date.day}.${date.month.toString().padLeft(2, '0')}.${date.year}",
    };
    final response = await get(url, queryParameters: params);
    if (response != null && response.statusCode == 200) {
      List<dynamic> jsonList = jsonDecode(jsonEncode(response.data));
      final trips = jsonList.map((item) => Trip.fromJson(item)).toList();
      globals.dataState.value = DataLoadingState.LOADED;
      return trips;
    } else {
      return [];
    }
  }

  Future<TripDetail> getTripDetail(int pointA, int pointB, String tripId, DateTime start) async {
    final String url = _buildUrl("/trip/");
    final params = {
      'point_a': pointA,
      'point_b': pointB,
      'trip_id': tripId,
      'start':
          "${start.year}-${start.month.toString().padLeft(2, '0')}-${start.day}T${start.hour.toString().padLeft(2, '0')}:${start.minute.toString().padLeft(2, '0')}:00"
    };
    final response = await get(url, queryParameters: params);
    if (response != null && response.statusCode == 200) {
      return TripDetail.fromRawJson(jsonEncode(response.data));
    } else {
      return TripDetail();
    }
  }

  Future<QRCreateResponse> qrCodeCreate() async {
    final url = _buildUrl("/external-auth/");
    final response = await post(url);
    if (response != null && response.statusCode == 201) {
      return QRCreateResponse.fromRawJson(response.toString());
    } else {
      return QRCreateResponse.fromJson({"error": "Не удалось создать QR-код"});
    }
  }

  Future<QRAuthStatusResponse> checkQRAuth(String uid) async {
    final url = _buildUrl("/external-auth/${uid}");
    final response = await get(url);
    if (response != null && response.statusCode == 200) {
      return QRAuthStatusResponse.fromRawJson(jsonEncode(response.data));
    } else {
      return QRAuthStatusResponse.fromJson({"error": "Не удалось проверить QR-код"});
    }
  }

  Future<DocTypesResponse> getDocumentsTypes(int pointA) async {
    final url = _buildUrl("/type-of-document/");
    final params = {'point_a': pointA};
    final response = await get(url, queryParameters: params);
    if (response != null && response.statusCode == 200) {
      return DocTypesResponse.fromRawJson(jsonEncode(response.data));
    } else {
      return DocTypesResponse(error: "Не удалось получить типы документов");
    }
  }

  Future<CountriesResponse> getCountries(int pointA) async {
    final url = _buildUrl("/country/");
    final params = {
      'point_a': pointA,
      'limit': 1000,
    };
    final response = await get(url, queryParameters: params);
    if (response != null && response.statusCode == 200) {
      return CountriesResponse.fromRawJson(jsonEncode(response.data));
    } else {
      return CountriesResponse(error: "Не удалось получить список стран");
    }
  }

  /// Бронирование
  Future<Result<CreateOrderModel>> createOrder(OrderToPlace orderToPlace) async {
    final url = _buildUrl("/order/");
    final response = await post(url, data: orderToPlace.toJson(), isShowError: false);
    if (response != null && response.statusCode == 200) {
      return Result.success(CreateOrderModel.fromRawJson(jsonEncode(response.data)));
    } else if (response != null && response.data is Map<String, dynamic>) {
      final error = ApiError.fromJson(response.data);
      return Result.failure(error);
    } else {
      return Result.failure(ApiError(message: "Не удалось создать бронирование"));
    }
  }

  /// Создание платежа
  /// paymentType: 1 - продажа, 2 - возврат, для СБП всегда 1
  /// paymentButton: 2 // Опциональное, если клиент выбрал оплату через доп.виды, если через терминал - не отправлять
  /// POST /app/order/<id заказа>/payment/
  Future<Result<CreatePaymentModel>> createPayment(int id, int paymentType, int? paymentButton) async {
    final url = _buildUrl("/order/$id/payment/");
    final params = {
      'payment_type': paymentType,
      if (paymentButton != null) 'payment_button': paymentButton,
    };
    final response = await post(url, data: params);
    if (response != null && response.statusCode == 200) {
      return Result.success(CreatePaymentModel.fromRawJson(response.toString()));
    } else if (response != null && response.data is Map<String, dynamic>) {
      final error = ApiError.fromJson(response.data);
      return Result.failure(error);
    } else {
      return Result.failure(ApiError(message: "Не удалось создать платеж"));
    }
  }

  /// Проверяем статус оплаты
  /// GET /app/order/<id заказа>/payment/<id платежа>/
  Future<Result<CreatePaymentModel>> getStatusPayment(int id, int idPayment) async {
    final url = _buildUrl("/order/$id/payment/$idPayment/");
    final response = await get(url);
    if (response != null && response.statusCode == 200) {
      return Result.success(CreatePaymentModel.fromRawJson(response.toString()));
    } else if (response != null && response.data is Map<String, dynamic>) {
      final error = ApiError.fromJson(response.data);
      return Result.failure(error);
    } else {
      return Result.failure(ApiError(message: "Не удалось проверить статус оплаты"));
    }
  }

  /// Проверяем статус заказа
  /// GET /app/order/<id заказа>/
  Future<Result<CreateOrderModel>> getStatusOrder(int id) async {
    final url = _buildUrl("/order/$id/");
    final response = await get(url);
    if (response != null && response.statusCode == 200) {
      return Result.success(CreateOrderModel.fromRawJson(response.toString()));
    } else if (response != null && response.data is Map<String, dynamic>) {
      final error = ApiError.fromJson(response.data);
      return Result.failure(error);
    } else {
      return Result.failure(ApiError(message: "Не удалось проверить статус заказа"));
    }
  }

  /// Выполнение команды на ККМ Сервер
  Future<Map<String, dynamic>?> executeCommand(PaymentData? data) async {
    final url = data?.url;
    Response? response;
    final dataCommand = data?.command?.toJson();

    if (data != null && data.method == "POST" && url != null) {
      response = await post(url, data: dataCommand);
    } else if (data != null && data.method == "GET" && url != null) {
      response = await get(url, queryParameters: dataCommand);
    }

    if (response?.statusCode == 200) {
      return response?.data as Map<String, dynamic>?;
    } else {
      return null;
    }
  }

  /// Выполнение команды на ККМ Сервер печать чека
  Future<Map<String, dynamic>?> executeCommandPrint(PrintCommand printCommand) async {
    final url = printCommand.url;
    Response? response;
    final dataCommand = printCommand.command?.toJson();

    if (printCommand.method == "POST" && url != null) {
      response = await post(url, data: dataCommand);
    } else if (printCommand.method == "GET" && url != null) {
      response = await get(url, queryParameters: dataCommand);
    }

    if (response?.statusCode == 200) {
      return response?.data as Map<String, dynamic>?;
    } else {
      return null;
    }
  }

  /// Отправляем ответ от ККМ
  /// POST /app/order/<id заказа>/payment/<id платежа>/
  Future<Result<CreatePaymentModel>> sendPaymentResult(int id, int idPayment, Map<String, dynamic>? data) async {
    final url = _buildUrl("/order/$id/payment/$idPayment/");
    final response = await post(url, data: data);
    if (response != null && response.statusCode == 200) {
      return Result.success(CreatePaymentModel.fromRawJson(response.toString()));
    } else if (response != null && response.data is Map<String, dynamic>) {
      final error = ApiError.fromJson(response.data);
      return Result.failure(error);
    } else {
      return Result.failure(ApiError(message: "Не удалось создать платеж"));
    }
  }

  /// Поиск билетов к возврату
  /// GET /app/order/tickets-to-return/<номер билета из поля ticket_number>/
  Future<Result<List<Ticket>>> getTicketsToReturn(String number) async {
    final url = _buildUrl("/order/tickets-to-return/$number/");
    final response = await get(url, isShowError: false);
    if (response != null && response.statusCode == 200) {
      final List<dynamic> jsonList = response.data as List<dynamic>;
      final List<Ticket> tickets = jsonList.map((json) => Ticket.fromJson(json)).toList();
      return Result.success(tickets);
    } else if (response != null && response.data is Map<String, dynamic>) {
      final error = ApiError.fromJson(response.data);
      return Result.failure(error);
    } else {
      return Result.failure(ApiError(message: "Не удалось найти ваши билеты"));
    }
  }

  /// Возврат билетов
  /// POST /app/order/tickets-return/
  Future<Result<CreateOrderModel>> ticketsReturn(List<String> numbers) async {
    final url = _buildUrl("/order/tickets-return/");
    final data = {'ticket_numbers': numbers};
    final response = await post(url, data: data, isShowError: false);
    if (response != null && response.statusCode == 200) {
      return Result.success(CreateOrderModel.fromRawJson(response.toString()));
    } else if (response != null && response.data is Map<String, dynamic>) {
      final error = ApiError.fromJson(response.data);
      return Result.failure(error);
    } else {
      return Result.failure(ApiError(message: "Не удалось создать платеж"));
    }
  }
}
