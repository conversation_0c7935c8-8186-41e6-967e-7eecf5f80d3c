import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/colors.dart';
import 'login/login_logic.dart';

class KeyboardButton extends StatelessWidget {
  final logic = Get.put(LoginLogic());
  final String text;

  // final Icon? icon ;
  final double left;
  final double top;
  final width;
  final TextEditingController? controller;

  // final VoidCallback? onTap;

  KeyboardButton({required this.text, required this.left, required this.top, required this.controller, this.width});

  void insertCharacter(TextEditingController controller, String text) {
    final currentText = controller.text;
    final selection = controller.selection;
    final position = selection.start;
    final newText = currentText.replaceRange(position, position, text);
    final newPosition = position + text.length;

    controller.value = controller.value.copyWith(
      text: newText,
      selection: TextSelection.collapsed(offset: newPosition),
      composing: TextRange.empty,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: left - 38,
      top: top - 15,
      child: AnimatedContainer(
        height: 77,
        width: width ?? 101,
        duration: const Duration(seconds: 2),
        curve: Curves.easeIn,
        child: Material(
          color: Color(0xFFE6F1FC),
          borderRadius: BorderRadius.circular(10),
          child: InkWell(
            onTap: () {
              insertCharacter(controller!, text);
            },
            borderRadius: BorderRadius.circular(10),
            splashColor: Color(0xFF1E90FF).withOpacity(0.3),
            child: Container(
              width: width ?? 101,
              height: 77,
              child: Center(
                child: Text(
                  text,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 40,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class SpecialKeyboardButton extends StatelessWidget {
  final Widget? icon;

  final double left;
  final double top;
  final double? width;
  final TextEditingController? controller;
  final VoidCallback? onTap;

  const SpecialKeyboardButton({this.icon, required this.left, required this.top, this.width, required this.controller, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: left - 38,
      top: top - 15,
      child: AnimatedContainer(
        height: 77,
        width: width ?? 101,
        duration: const Duration(seconds: 2),
        curve: Curves.easeIn,
        child: Material(
          color: Color(0xFFE6F1FC),
          borderRadius: BorderRadius.circular(10),
          child: InkWell(
            onTap: () {
              onTap!();
            },
            borderRadius: BorderRadius.circular(10),
            splashColor: Color(0xFF1E90FF).withOpacity(0.3),
            child: Container(
              width: width ?? 101,
              height: 77,
              child: Center(child: icon ?? Container()),
            ),
          ),
        ),
      ),
    );
  }
}

class Keyboard extends StatelessWidget {
  Keyboard(this.controller, {this.numbers = true});

  final TextEditingController controller;
  final bool numbers;

  static RxBool englishPressed = false.obs;
  final logic = Get.put(LoginLogic());

  static void setLanguage(bool isEnglish) {
    englishPressed.value = isEnglish;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1920,
      height: 410,
      child: Stack(
        children: [
          Positioned(
            left: 0,
            top: 0,
            child: Container(
              width: 1920,
              height: 410,
              decoration: BoxDecoration(color: Color(0xFFF1F8FF)),
            ),
          ),
          Positioned(
            left: 111,
            top: 37,
            child: Obx(() {
              return Container(
                width: 1312,
                height: 337,
                // clipBehavior: Clip.antiAlias,
                // decoration: BoxDecoration(),
                child: Stack(
                  children: [
                    KeyboardButton(
                      text: englishPressed.value ? 'Q' : 'Й',
                      left: 35.19,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'A' : 'Ф',
                      left: 88.22,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'Z' : 'Я',
                      left: 146.20,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'X' : 'Ч',
                      left: 254.48,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'C' : 'С',
                      left: 364,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'V' : 'М',
                      left: 475,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'B' : 'И',
                      left: 583.19,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'N' : 'Т',
                      left: 697.28,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'M' : 'Ь',
                      left: 806.54,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? '<' : 'Б',
                      left: 916.16,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? '>' : 'Ю',
                      left: 1025,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'S' : 'Ы',
                      left: 197.03,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'D' : 'В',
                      left: 308,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'F' : 'А',
                      left: 418,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'G' : 'П',
                      left: 529.70,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'H' : 'Р',
                      left: 642,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'J' : 'О',
                      left: 749,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'K' : 'Л',
                      left: 860,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'L' : 'Д',
                      left: 970,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? '-' : 'Ж',
                      left: 1080,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? '_' : 'Э',
                      left: 1193,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'W' : 'Ц',
                      left: 145,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'E' : 'У',
                      left: 256,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'R' : 'К',
                      left: 368.02,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'T' : 'Е',
                      left: 480.36,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'Y' : 'Н',
                      left: 589,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'U' : 'Г',
                      left: 698.77,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'I' : 'Ш',
                      left: 808,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'O' : 'Щ',
                      left: 919,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? 'P' : 'З',
                      left: 1029.83,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? '[' : 'Х',
                      left: 1137.89,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? ']' : 'Ъ',
                      left: 1245.85,
                      top: 17,
                      controller: controller,
                    ),
                    SpecialKeyboardButton(
                      left: 193,
                      top: 278,
                      controller: null,
                      onTap: () {
                        englishPressed.value = !englishPressed.value;
                      },
                      icon: Container(
                        width: 42,
                        height: 42,
                        child: Image.asset("lib/assets/images/ic24-globe.png"),
                      ),
                    ),
                    SpecialKeyboardButton(
                      left: 1133,
                      top: 191,
                      icon: Container(
                        width: 35,
                        height: 35,
                        child: Image.asset("lib/assets/images/ic24-arrow-left.png"),
                      ),
                      controller: controller,
                      onTap: () {
                        final position = controller.selection.base.offset;
                        String newText = "";
                        int newPosition = 0;
                        if (position > 0) {
                          newText = controller.text.substring(0, position - 1) + controller.text.substring(position);
                          newPosition = position - 1;
                        }

                        controller.value = controller.value.copyWith(
                          text: newText,
                          selection: TextSelection.collapsed(offset: newPosition),
                          composing: TextRange.empty,
                        );

                        // logic.currentTextController.value.text = logic.currentTextController.value.text.substring(0, logic.currentTextController.value.text.length - 1);
                        // logic.currentTextController.value.selection = TextSelection.fromPosition(position) ;
                        // logic.currentTextController.value.;
                      },
                    ),
                    SpecialKeyboardButton(
                      left: 303,
                      top: 278,
                      width: englishPressed.value ? 560 : 670,
                      controller: controller,
                      onTap: () {
                        final currentText = controller.text;
                        final position = controller.selection.base.offset;
                        final newText = currentText.substring(0, position) + ' ' + currentText.substring(position);

                        controller.value = controller.value.copyWith(
                          text: newText,
                          selection: TextSelection.collapsed(offset: position + 1),
                          composing: TextRange.empty,
                        );
                      },
                    ),
                    if (englishPressed.value)
                      KeyboardButton(
                        text: englishPressed.value ? '@' : '@',
                        left: 870,
                        top: 278,
                        controller: controller,
                      ),
                    KeyboardButton(
                      text: englishPressed.value ? '_' : '_',
                      left: 980,
                      top: 278,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: englishPressed.value ? '.' : '.',
                      left: 1090,
                      top: 278,
                      controller: controller,
                    ),
                  ],
                ),
              );
            }),
          ),
          if (numbers)
            Positioned(
              left: 1483,
              top: 37,
              child: Container(
                width: 330,
                height: 337,
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(),
                child: Stack(
                  children: [
                    KeyboardButton(
                      text: '7',
                      left: 39.50,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: '8',
                      left: 153.50,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: '9',
                      left: 267.50,
                      top: 17,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: '4',
                      left: 39.50,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: '5',
                      left: 153.50,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: '6',
                      left: 267.50,
                      top: 104,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: '1',
                      left: 39.50,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: '2',
                      left: 153.50,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: '3',
                      left: 267.50,
                      top: 191,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: '0',
                      left: 39.50,
                      top: 277,
                      width: 215.0,
                      controller: controller,
                    ),
                    KeyboardButton(
                      text: '-',
                      left: 267.50,
                      top: 277,
                      controller: controller,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class DottedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Color(0xFF1E90FF) // Цвет линии
      ..strokeWidth = 2.0 // Толщина линии
      ..style = PaintingStyle.stroke;

    final dashWidth = 15.0; // Длина каждой точки
    final dashSpace = 10.0; // Промежуток между точками
    double startX = 0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

//MARK: - Отображаем ошибку
class ErrorHandler {
  static void showError(String message) {
    Get.snackbar(
      'Ошибка',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: AppColors.errorBackground,
      margin: EdgeInsets.symmetric(horizontal: 670, vertical: 30),
      borderRadius: 12,
      icon: Icon(Icons.error, color: Colors.red),
      duration: Duration(seconds: 3),
    );
  }
}

//MARK: - Отображаем ворнинг
class WarningHandler {
  static void showWarning(String title, String message) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: AppColors.primary,
      colorText: Colors.white,
      margin: EdgeInsets.symmetric(horizontal: 670, vertical: 30),
      borderRadius: 12,
      duration: Duration(seconds: 3),
    );
  }
}

//MARK: - Обвеотка кнопки с эффектом нажатия
class CustomInkWell extends StatelessWidget {
  const CustomInkWell({
    super.key,
    this.height,
    this.width,
    this.color,
    this.splashColor,
    required this.borderRadius,
    required this.onTap,
    required this.child,
  });
  final double? height;
  final double? width;
  final double borderRadius;
  final Function() onTap;
  final Widget child;
  final Color? color;
  final Color? splashColor;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      height: height,
      width: width,
      duration: const Duration(seconds: 2),
      curve: Curves.easeIn,
      child: Material(
        color: color,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          splashColor: splashColor,
          child: Container(
            width: width,
            height: height,
            child: child,
          ),
        ),
      ),
    );
  }
}
