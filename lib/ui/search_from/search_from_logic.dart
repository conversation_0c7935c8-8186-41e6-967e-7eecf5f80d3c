import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rxdart/rxdart.dart';
import 'package:terminal/src/globals.dart';
import '../../models/stations_model.dart';
import '../../src/api.dart';

class SearchFromLogic extends GetxController {
  final api = Get.find<Api>();
  final globals = Get.find<Globals>();

  TextEditingController searchTextController = TextEditingController();
  FocusNode searchFocusNode = FocusNode();
  final searchText = "".obs;
  final fromStations = <Station>[].obs;
  final dataState = DataLoadingState.LOADING.obs;

  @override
  void onInit() async {
    super.onInit();

    dataState.value = DataLoadingState.LOADING;

    searchTextController.addListener(() {
      searchText.value = searchTextController.text;
    });

    searchText.stream.debounceTime(Duration(milliseconds: 10)).listen((value) async {
      if (searchTextController.text.isEmpty) {
        try {
          final res = await api.getPopularDispatchStationsByName(false);
          fromStations.value = res ?? [];
          dataState.value = DataLoadingState.LOADED;
        } catch (_) {
          dataState.value = DataLoadingState.ERROR;
        }
      } else {
        try {
          final res = await api.getDispatchStationsByName(searchTextController.text);
          fromStations.value = res ?? [];
          dataState.value = DataLoadingState.LOADED;
        } catch (_) {
          dataState.value = DataLoadingState.ERROR;
        }
      }
    });
  }

  List<Widget> generateFromStations(List<Station> stations) {
    List<Widget> widgets = [];

    const cityStyle = TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.w800);

    Widget mainCatalog(Station station) {
      return InkWell(
        onTap: () {
          globals.selectedStationFrom.value = station;
          Get.toNamed("/searchToScreen");
        },
        child: Container(
          width: 376,
          height: 90,
          padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 32),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.85),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
            border: Border.all(
              color: Color(0x191E90FF),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "${station.title}",
                style: cityStyle,
                maxLines: null,
              ),
              Text(
                "Все станции",
                style: TextStyle(
                  color: Color(0xFF1E90FF),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              )
            ],
          ),
        ),
      );
    }

    Widget oneStationInCatalog(Station station) {
      return InkWell(
        onTap: () {
          globals.selectedStationFrom.value = station;
          Get.toNamed("/searchToScreen");
        },
        child: Container(
          width: 376,
          // height: 90,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.8500000238418579),
            border: Border.all(color: Color(0x191E90FF)),
          ),
          // padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 12),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${station.title}",
                  style: cityStyle,
                  maxLines: null,
                ),
                if ((station.address ?? "").isNotEmpty) Text("${station.address}")
              ],
            ),
          ),
        ),
      );
    }

    Widget oneStationItem(Station station) {
      return InkWell(
        onTap: () {
          globals.selectedStationFrom.value = station;
          Get.toNamed("/searchToScreen");
        },
        child: Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Container(
            width: 376,
            // height: 130,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.85),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "${station.title}",
                    style: cityStyle,
                    maxLines: null,
                  ),
                  if ((station.address ?? "").isNotEmpty) Text("${station.address}")
                ],
              ),
            ),
          ),
        ),
      );
    }

    Widget lastStationItem(Station station) {
      return InkWell(
        onTap: () {
          globals.selectedStationFrom.value = station;
          Get.toNamed("/searchToScreen");
        },
        child: Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Container(
            width: 376,
            // height: 190,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.85),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
              border: Border.all(
                color: Color(0x191E90FF),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "${station.title}",
                    style: cityStyle,
                    maxLines: null,
                  ),
                  if ((station.address ?? "").isNotEmpty) Text("${station.address}")
                ],
              ),
            ),
          ),
        ),
      );
    }

    /// Creates a catalog of stations and adds them to the widgets list.
    void createCatalog(List<Station> stations) {
      Map<String, List<Station>> catalogMap = {};

      // Сначала создаем карту каталогов и их подэлементов
      for (var station in stations) {
        if (station.isCatalog!) {
          catalogMap[station.id!] = [];
        } else if (station.catalogId != null) {
          if (!catalogMap.containsKey(station.catalogId)) {
            catalogMap[station.catalogId!] = [];
          }
          catalogMap[station.catalogId!]!.add(station);
        }
      }

      // Теперь обрабатываем каждый каталог и его подэлементы
      for (var station in stations) {
        if (station.isCatalog!) {
          widgets.add(mainCatalog(station));
          var subItems = catalogMap[station.id!] ?? [];
          for (int i = 0; i < subItems.length; i++) {
            var subItem = subItems[i];
            if (i == 0 || !(i == subItems.length - 1)) {
              widgets.add(oneStationInCatalog(subItem));
            } else if (i == subItems.length - 1) {
              widgets.add(lastStationItem(subItem));
            }
          }
        } else if (station.catalogId == null) {
          widgets.add(oneStationItem(station));
        }
      }
    }

    if (stations.isEmpty) {
      return [];
    } else {
      createCatalog(stations);
    }

    return widgets;
  }
}
