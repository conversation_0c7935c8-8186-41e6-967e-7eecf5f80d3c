import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import '../../src/globals.dart';
import 'select_date_logic.dart';

class UniversalCalendar extends StatelessWidget {
  final logic = Get.put(SelectDateLogic());
  final globals = Get.find<Globals>();

  // final int year;
  // final int month;

  UniversalCalendar();

  String getRussianMonthName(int month) {
    // Список русских названий месяцев
    List<String> monthNames = ['Январь', 'Февраль', 'Март', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь'];

    // Проверка на корректность номера месяца
    if (month < 1 || month > 12) {
      throw ArgumentError('Номер месяца должен быть от 1 до 12');
    }

    // Возвращаем название месяца
    return monthNames[month - 1];
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        width: 1920,
        height: 1080,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(color: Colors.white),
        child: Stack(
          children: [
            Positioned(
              left: 573,
              top: 80,
              child: Text(
                'Выберите дату поездки',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 64,
                  fontWeight: FontWeight.w900,
                ),
              ),
            ),
            Positioned(
              left: 732,
              top: 197,
              child: Row(
                children: [
                  _buildDateButton('Сегодня', DateTime.now()),
                  _buildDateButton('Завтра', DateTime.now().add(Duration(days: 1))),
                  _buildDateButton('Послезавтра', DateTime.now().add(Duration(days: 2))),
                ],
              ),
            ),
            Positioned(
              left: 635,
              top: 291,
              child: Container(
                width: 640,
                height: 650,
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    side: BorderSide(width: 2, color: Color(0xFFE6E6E6)),
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Stack(children: [
                  Positioned(
                    left: 76,
                    right: 76,
                    top: 20,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          onTap: () {
                            logic.selectedDay.value = DateTime(logic.selectedDay.value.year, logic.selectedDay.value.month - 1, 1);
                          },
                          child: Container(width: 50, height: 50, padding: EdgeInsets.all(10), child: Image.asset("lib/assets/images/big_left.png", width: 20)),
                        ),
                        Text(
                          getRussianMonthName(logic.selectedDay.value.month) + " " + logic.selectedDay.value.year.toString(),
                          style: TextStyle(
                            color: Color(0xFFB3BDC8),
                            fontSize: 36,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            logic.selectedDay.value = DateTime(logic.selectedDay.value.year, logic.selectedDay.value.month + 1, 1);
                          },
                          child:
                              Container(width: 50, height: 50, padding: EdgeInsets.all(10), child: Image.asset("lib/assets/images/big_right.png", width: 20)),
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    left: 35,
                    top: 90,
                    right: 35,
                    child: _buildWeekDays(),
                  ),
                  Positioned(
                    left: 30,
                    top: 150,
                    right: 30,
                    // bottom: 140,
                    child: _buildCalendarDays(),
                  ),
                ]),
              ),
            ),
            BackOrMainButton(),
            Positioned(
              left: 1682,
              top: 941,
              child: CustomInkWell(
                color: AppColors.primary,
                splashColor: AppColors.primarySplashColor,
                borderRadius: 12,
                onTap: () {
                  globals.selectedDate.value = logic.selectedDay.value;
                  Get.toNamed("/selectRoutesScreen");
                },
                child: _buildButton('Далее'),
              ),
            ),
            Positioned(
              left: 69,
              top: 941,
              child: MainBackButton(),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildDateButton(String text, DateTime dateTime) {
    return InkWell(
      onTap: () {
        logic.selectedDay.value = dateTime;
      },
      child: Padding(
        padding: const EdgeInsets.only(right: 16),
        child: Container(
          // width: 129,
          // height: 48,
          padding: const EdgeInsets.only(
            top: 11,
            left: 15,
            right: 15,
            bottom: 11,
          ),
          clipBehavior: Clip.antiAlias,
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              side: BorderSide(width: 2, color: Color(0xFF1E90FF)),
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                text,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWeekDays() {
    const List<String> weekDays = ['П', 'В', 'С', 'Ч', 'П', 'С', 'В'];
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: weekDays.map((day) {
        return SizedBox(
          width: 64,
          height: 39,
          child: Text(
            day,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color(0xFF1E90FF),
              fontSize: 32,
              fontWeight: FontWeight.w700,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCalendarDays() {
    // log(logic.selectMonth.value.toString());

    final DateTime firstDayOfMonth = DateTime(logic.selectYear.value, logic.selectMonth.value, 1);
    final DateTime lastDayOfMonth = DateTime(logic.selectYear.value, logic.selectMonth.value + 1, 0);
    final int daysInMonth = lastDayOfMonth.day;
    final int firstWeekdayOfMonth = firstDayOfMonth.weekday;

    List<Widget> dayWidgets = [];

    // Add empty spaces for days before the first day of the month
    for (int i = 1; i < firstWeekdayOfMonth; i++) {
      dayWidgets.add(SizedBox(width: 56, height: 56));
    }

    // Add days of the month
    for (int day = 1; day <= daysInMonth; day++) {
      final DateTime currentDate = DateTime(logic.selectYear.value, logic.selectMonth.value, day);
      final bool isPastDate = currentDate.isBefore(DateTime.now().subtract(Duration(days: 1)));

      final price = logic.prices["${currentDate.day}.${currentDate.month}.${currentDate.year}"].toString();
      // log(price.toString()) ;

      if (DateUtils.isSameDay(currentDate, logic.selectedDay.value) == true) {
        dayWidgets.add(
          Container(
            width: 56,
            height: 56,
            decoration: ShapeDecoration(
              color: Color(0xFF1E90FF),
              shape: RoundedRectangleBorder(
                side: BorderSide(width: 2, color: Color(0xFF1E90FF)),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '$day',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isPastDate ? Color(0xFFB3BDC8) : Colors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (price != "null" && price != "")
                    Text(
                      "$price₽",
                      style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w700),
                    ),
                ],
              ),
            ),
          ),
        );
      } else {
        dayWidgets.add(
          InkWell(
            onTap: () {
              if (isPastDate) return;
              logic.selectedDay.value = DateTime(logic.selectYear.value, logic.selectMonth.value, day);
            },
            child: Container(
              width: 56,
              height: 56,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '$day',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: isPastDate ? Color(0xFFB3BDC8) : Color(0xFF333333),
                        fontSize: 32,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    if (price != "null" && price != "")
                      Text(
                        price,
                        style: TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      }
    }

    // Ensure the grid has exactly 7 columns and 5 rows
    while (dayWidgets.length < 35) {
      dayWidgets.add(SizedBox(width: 56, height: 56));
    }

    return GridView.count(
      crossAxisCount: 7,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      children: dayWidgets,
    );
  }

  Widget _buildButton(String text, {bool isLight = false}) {
    return Container(
      padding: const EdgeInsets.only(
        top: 20,
        left: 30,
        right: 30,
        bottom: 16,
      ),
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            text,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isLight ? Colors.black : Colors.white,
              fontSize: 36,
              fontWeight: FontWeight.w900,
            ),
          ),
        ],
      ),
    );
  }
}

class SelectDateScreen extends StatelessWidget {
  SelectDateScreen({Key? key}) : super(key: key);

  final globals = Get.find<Globals>();
  final logic = Get.put(SelectDateLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: UniversalCalendar(),
    );
  }
}
