import 'dart:developer';
import 'package:get/get.dart';
import '../../src/api.dart';
import '../../src/globals.dart';

class SelectDateLogic extends GetxController {
  final api = Get.find<Api>();
  final globals = Get.find<Globals>();

  final selectYear = 0.obs;
  final selectMonth = 0.obs;
  final selectedDay = DateTime.now().obs;

  final prices = <String, dynamic>{}.obs;

  int prevMonth = 0;

  @override
  void onInit() {
    // TODO: implement onInit
    selectedDay.value = DateTime.now();
    selectYear.value = DateTime.now().year;

    prevMonth = selectMonth.value;
    selectMonth.value = DateTime.now().month;

    selectedDay.listen((d) {
      selectYear.value = d.year;
      selectMonth.value = d.month;
    });

    selectMonth.listen((m) async {
      if (m != prevMonth) {
        prevMonth = m;
        log("WOW!! $m ${DateTime.now().month}");

        if (m == DateTime.now().month) {
          selectedDay.value = DateTime(selectYear.value, m, DateTime.now().day);
        } else {
          selectedDay.value = DateTime(selectYear.value, m, 1);
        }
        final res = await api.getPrices(globals.selectedStationFrom.value.id ?? "", globals.selectedStationTo.value.id ?? "");
        prices.value = res ?? {};
      }
    });

    super.onInit();
  }
}
