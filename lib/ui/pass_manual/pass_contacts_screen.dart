import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import '../../src/globals.dart';
import '../helpers.dart';
import 'pass_manual_logic.dart';

//MARK: - Введите контактные данные
class PassContactsScreen extends StatelessWidget {
  PassContactsScreen({Key? key}) : super(key: key);

  final logic = Get.put(PassManualLogic());
  final globals = Get.find<Globals>();
  final currentScreen = PassengerScreen.PassportScreen.obs;
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey,
        body: Form(
          key: _formKey,
          child: Container(
              width: 1920,
              height: 1080,
              color: Colors.white,
              child: Stack(children: [
                BackOrMainButton(),
                title(),
                subTitle(),
                Positioned(
                  top: 270,
                  left: 60,
                  right: 60,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    spacing: 18,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        spacing: 18,
                        children: [
                          /// E-mail
                          // Container(
                          //   width: 590,
                          //   height: 150,
                          //   child: Column(
                          //     mainAxisAlignment: MainAxisAlignment.start,
                          //     crossAxisAlignment: CrossAxisAlignment.center,
                          //     children: [
                          //       Expanded(
                          //         child: TextFormField(
                          //           validator: (value) {
                          //             if (value == null || value.isEmpty) {
                          //               return 'Поле не может быть пустым';
                          //             }
                          //             return null;
                          //           },
                          //           autofocus: true,
                          //           decoration: InputDecoration(
                          //             hintText: "",
                          //             hintStyle: TextStyle(color: Colors.grey),
                          //             focusedBorder: UnderlineInputBorder(
                          //               borderSide: BorderSide(color: AppColors.primary, width: 2),
                          //             ),
                          //             enabledBorder: UnderlineInputBorder(
                          //               borderSide: BorderSide(color: AppColors.enabledBorder, width: 2),
                          //             ),
                          //           ),
                          //           cursorColor: Colors.black,
                          //           cursorHeight: 64,
                          //           cursorWidth: 2,
                          //           cursorRadius: Radius.zero,
                          //           onTapOutside: (event) {
                          //             FocusScope.of(context).requestFocus(logic.emailFocusNode);
                          //           },
                          //           showCursor: true,
                          //           focusNode: logic.emailFocusNode,
                          //           controller: logic.emailController,
                          //           style: TextStyle(
                          //             color: Colors.black,
                          //             fontSize: 64,
                          //             fontWeight: FontWeight.w400,
                          //           ),
                          //           textAlign: TextAlign.center,
                          //         ),
                          //       ),
                          //       Obx(() {
                          //         return Text(
                          //           "E-mail",
                          //           style: TextStyle(
                          //             color: logic.isEmailFocused.value ? AppColors.primary : AppColors.enabledBorder,
                          //             fontSize: 36,
                          //             fontWeight: FontWeight.w100,
                          //           ),
                          //         );
                          //       }),
                          //     ],
                          //   ),
                          // ),

                          /// Номер телефона
                          Container(
                            width: 590,
                            height: 150,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    validator: (value) {
                                      if (globals.tripOrder.noticeInfoMessage.value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Поле не может быть пустым';
                                        }
                                        if (value.length != 18) {
                                          return 'Введите корректный номер телефона';
                                        }
                                        final phoneRegex = RegExp(r'^\+7 \(\d{3}\) \d{3}-\d{2}-\d{2}$');
                                        if (!phoneRegex.hasMatch(value)) {
                                          return 'Введите корректный номер телефона';
                                        }
                                      }
                                      return null;
                                    },
                                    autofocus: true,
                                    decoration: InputDecoration(
                                      hintText: "",
                                      hintStyle: TextStyle(color: Colors.grey),
                                      focusedBorder: UnderlineInputBorder(
                                        borderSide: BorderSide(color: AppColors.primary, width: 2),
                                      ),
                                      enabledBorder: UnderlineInputBorder(
                                        borderSide: BorderSide(color: AppColors.enabledBorder, width: 2),
                                      ),
                                    ),
                                    cursorColor: Colors.black,
                                    cursorHeight: 64,
                                    cursorWidth: 2,
                                    cursorRadius: Radius.zero,
                                    onTapOutside: (event) {
                                      FocusScope.of(context).requestFocus(logic.phoneFocusNode);
                                    },
                                    showCursor: true,
                                    focusNode: logic.phoneFocusNode,
                                    controller: logic.phoneController,
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 64,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Obx(() {
                                  return Text(
                                    "Номер телефона",
                                    style: TextStyle(
                                      color: logic.isPhoneFocused.value ? AppColors.primary : AppColors.enabledBorder,
                                      fontSize: 36,
                                      fontWeight: FontWeight.w100,
                                    ),
                                  );
                                }),
                              ],
                            ),
                          ),
                        ],
                      ),
                      (globals.tripOrder.selectedTripDetail.value.noticeInfo?.noticeAvailable ?? false) ? cancelAlertMessageBlock() : Container(),
                    ],
                  ),
                ),
                Obx(() {
                  return Positioned(bottom: 0, left: 0, right: 0, child: Keyboard(logic.currentTextController.value));
                }),
                backMainButton(),
                nextButton()
              ])),
        ));
  }

  Widget title() {
    return Positioned(
      top: 80,
      left: 175,
      right: 175,
      child: Text(
        'Введите контактные данные',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.black,
          fontSize: 64,
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  Widget subTitle() {
    return Positioned(
      top: 200,
      left: 175,
      right: 175,
      child: Text(
        'На указанный телефон отправим уведомление об отмене рейса',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.black,
          fontSize: 32,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget cancelAlertMessageBlock() {
    return GestureDetector(
      onTap: () {
        globals.tripOrder.noticeInfoMessage.toggle();
      },
      child: Container(
        width: 560,
        height: 44,
        child: Row(
          children: [
            Obx(() {
              return Container(
                width: 44,
                height: 44,
                decoration: ShapeDecoration(
                  color: Color(0xFFF1F8FF),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(width: 2, color: Color(0xFF1E90FF)),
                  ),
                ),
                child: globals.tripOrder.noticeInfoMessage.value
                    ? Center(
                        child: Image.asset(
                          "lib/assets/images/check.png",
                          width: 20,
                          height: 20,
                        ),
                      )
                    : Container(),
              );
            }),
            SizedBox(width: 18),
            Text(
              'Оповещение об отмене рейса',
              style: TextStyle(
                color: Colors.black,
                fontSize: 32,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  backMainButton() {
    return Positioned(
      left: 68,
      bottom: 446,
      child: MainBackButton(),
    );
  }

  nextButton() {
    return Positioned(
      bottom: 446,
      right: 61,
      child: Opacity(
        opacity: 1,
        child: InkWell(
          onTap: () {
            if (_formKey.currentState!.validate()) {
              //globals.tripOrder.orderToPlace.value.buyerEmail = logic.emailController.text;
              globals.tripOrder.orderToPlace.value.buyerPhone = logic.phoneController.text;
              Get.toNamed('/orderCheckScreen');
            }
          },
          child: Container(
            width: 180,
            height: 82,
            padding: const EdgeInsets.only(
              top: 20,
              left: 30,
              right: 30,
              bottom: 16,
            ),
            clipBehavior: Clip.antiAlias,
            decoration: ShapeDecoration(
              color: Color(0xFF1E90FF),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Далее',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.w900,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
