import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import '../../src/globals.dart';
import '../helpers.dart';
import 'pass_manual_logic.dart';

class PassportScreen extends StatelessWidget {
  PassportScreen({Key? key}) : super(key: key);

  final logic = Get.put(PassManualLogic());
  final globals = Get.find<Globals>();
  final currentScreen = PassengerScreen.PassportScreen.obs;
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey,
      body: Form(
        key: _formKey,
        child: Container(
          width: 1920,
          height: 1080,
          color: Colors.white,
          child: Stack(
            children: [
              BackOrMainButton(
                additionalAction: () {
                  final isEdit = Get.arguments?['isEdit'];
                  if (isEdit != null && isEdit != true) {
                    if (logic.passengerNumber.value > 0) {
                      logic.passengerNumber.value--;
                    }
                  }
                },
              ),
              Obx(() {
                return Positioned(
                  top: 80,
                  left: 205,
                  right: 205,
                  child: Text(
                    'Введите данные пассажира №${logic.passengerNumber.value + 1}',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 64,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                );
              }),

              //Гражданство
              Positioned(
                top: 250,
                left: 60,
                child: Container(
                  width: 590,
                  height: 150,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: TextFormField(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Поле не может быть пустым';
                            }
                            return null;
                          },
                          // enabled: false,
                          decoration: InputDecoration(
                            hintText: "",
                            hintStyle: TextStyle(color: Colors.grey),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppColors.primary, width: 2),
                            ),
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppColors.enabledBorder, width: 2),
                            ),
                          ),
                          cursorColor: Colors.black,
                          cursorHeight: 64,
                          cursorWidth: 2,
                          cursorRadius: Radius.zero,
                          onTapOutside: (event) {
                            FocusScope.of(context).requestFocus(logic.citizenshipFocusNode);
                          },
                          showCursor: true,
                          focusNode: logic.citizenshipFocusNode,
                          controller: logic.citizenshipController,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 64,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Obx(() {
                        return Text(
                          "Гражданство",
                          style: TextStyle(
                            color: logic.isCitizenshipFocused.value ? Colors.blue : AppColors.enabledBorder,
                            fontSize: 36,
                            fontWeight: FontWeight.w100,
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),

              //Тип документа
              Positioned(
                top: 250,
                left: 665,
                child: Container(
                  width: 590,
                  height: 150,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: TextFormField(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Поле не может быть пустым';
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                            hintText: "",
                            hintStyle: TextStyle(color: Colors.grey),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppColors.primary, width: 2),
                            ),
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppColors.enabledBorder, width: 2),
                            ),
                          ),
                          cursorColor: Colors.black,
                          cursorHeight: 64,
                          cursorWidth: 2,
                          cursorRadius: Radius.zero,
                          onTapOutside: (event) {
                            FocusScope.of(context).requestFocus(logic.documentTypeFocusNode);
                          },
                          showCursor: true,
                          focusNode: logic.documentTypeFocusNode,
                          controller: logic.documentTypeController,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 64,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Obx(() {
                        return Text(
                          "Тип документа",
                          style: TextStyle(
                            color: logic.isDocumentTypeFocused.value ? Colors.blue : AppColors.enabledBorder,
                            fontSize: 36,
                            fontWeight: FontWeight.w100,
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),

              //Серия и номер документа
              Positioned(
                top: 250,
                left: 1271,
                child: Container(
                  width: 590,
                  height: 150,
                  child: Column(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.center, children: [
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              child: Obx(
                            () => TextFormField(
                              validator: logic.validateDocumentNumber,
                              decoration: InputDecoration(
                                hintText:
                                    globals.docTypeList.firstWhereOrNull((val) => val.id == globals.tripOrder.selectedDocumentTypeId.value)?.maskExample ?? '',
                                hintStyle: TextStyle(color: Colors.grey),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(color: AppColors.primary, width: 2),
                                ),
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(color: AppColors.enabledBorder, width: 2),
                                ),
                              ),
                              cursorColor: Colors.black,
                              cursorHeight: 64,
                              cursorWidth: 2,
                              cursorRadius: Radius.zero,
                              onTapOutside: (event) {
                                FocusScope.of(context).requestFocus(logic.documentNumberFocusNode);
                              },
                              showCursor: true,
                              focusNode: logic.documentNumberFocusNode,
                              controller: logic.seraiNumberController,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 64,
                                fontWeight: FontWeight.w400,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          )),
                          InkWell(
                              onTap: () {
                                Get.dialog(Dialog(
                                    insetAnimationCurve: Curves.easeIn,
                                    insetAnimationDuration: const Duration(milliseconds: 50),
                                    insetPadding: EdgeInsets.zero,
                                    child: passportHelperDialog()));
                              },
                              child: Image.asset(
                                'lib/assets/images/qmark.png',
                                width: 76,
                              ))
                        ],
                      ),
                    ),
                    Obx(() {
                      return Text(
                        "Серия и номер документа",
                        style: TextStyle(
                          color: logic.isDocumentNumberFocused.value ? Colors.blue : AppColors.enabledBorder,
                          fontSize: 36,
                          fontWeight: FontWeight.w100,
                        ),
                      );
                    }),
                  ]),
                ),
              ),
              backMainButton(),
              nextButton(),
              Obx(
                () {
                  return Positioned(bottom: 0, left: 0, right: 0, child: Keyboard(logic.currentTextController.value));
                },
              )
            ],
          ),
        ),
      ),
    );
  }

  passportHelperDialog() {
    return Container(
      width: 900,
      // height: 625,
      padding: const EdgeInsets.symmetric(horizontal: 60, vertical: 80),
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Серия и номер документа',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
              fontSize: 48,
              fontWeight: FontWeight.w900,
            ),
          ),
          const SizedBox(height: 36),
          SizedBox(
            width: 780,
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: 'Для свидетельства о рождении серия и номер документа должны быть указаны в следующем формате: \n\n',
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 32,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextSpan(
                    text: '1-4 римских цифры, 2 русских буквы, 6 цифр',
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 32,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 36),
          Container(
            width: 440,
            height: 117,
            child: Stack(
              children: [
                Positioned(
                  left: 0,
                  top: 0,
                  child: Container(
                    width: 440,
                    height: 72,
                    child: Stack(
                      children: [
                        Positioned(
                          left: 0,
                          top: 0,
                          child: Container(
                            width: 440,
                            height: 72,
                            decoration: ShapeDecoration(
                              color: Color(0xFFE7D5B3),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          left: 35,
                          top: 8,
                          child: Text(
                            'III-AM № 234567',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Color(0xFF692823),
                              fontSize: 48,
                              fontFamily: 'Times New Roman',
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  left: 194,
                  top: 6,
                  child: Container(
                    width: 59,
                    height: 63,
                    decoration: ShapeDecoration(
                      color: Color(0x19FF0000),
                      shape: RoundedRectangleBorder(
                        side: BorderSide(width: 1, color: Color(0xFFFF0000)),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 223,
                  top: 69,
                  child: Transform(
                    transform: Matrix4.identity()
                      ..translate(0.0, 0.0)
                      ..rotateZ(1.57),
                    child: Container(
                      width: 26,
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          side: BorderSide(
                            width: 1,
                            strokeAlign: BorderSide.strokeAlignCenter,
                            color: Color(0xFFFF0000),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 115,
                  top: 94,
                  child: Text(
                    'этот символ не указывать',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Color(0xFFFF0000),
                      fontSize: 18,
                      fontFamily: 'Overpass',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  backMainButton() {
    return Positioned(
      left: 68,
      top: 552,
      child: MainBackButton(),
    );
  }

  nextButton() {
    return Positioned(
        bottom: 446,
        right: 61,
        child: Opacity(
          opacity: 1,
          child: CustomInkWell(
            color: AppColors.primary,
            splashColor: AppColors.primarySplashColor,
            borderRadius: 12,
            onTap: () {
              if (_formKey.currentState!.validate()) {
                if (Get.arguments?['isEdit'] == true) {
                  Get.toNamed('/passCheckScreen');
                } else {
                  Get.toNamed('/passNameScreen');
                }
              }
            },
            child: Container(
              width: 180,
              height: 82,
              padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
              clipBehavior: Clip.antiAlias,
              decoration: ShapeDecoration(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Далее',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 36,
                      fontWeight: FontWeight.w900,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
