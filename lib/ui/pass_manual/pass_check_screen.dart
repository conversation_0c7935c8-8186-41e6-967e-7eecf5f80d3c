import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import '../helpers.dart';
import 'pass_manual_logic.dart';

class PassCheckScreen extends StatelessWidget {
  PassCheckScreen({Key? key}) : super(key: key);

  final logic = Get.put(PassManualLogic());
  final globals = Get.find<Globals>();
  final currentScreen = PassengerScreen.PassportScreen.obs;
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey,
      body: Form(
        key: _formKey,
        child: Container(
          width: 1920,
          height: 1080,
          color: Colors.white,
          child: Stack(
            children: [
              BackOrMainButton(),
              Obx(() {
                return Positioned(
                  top: 80,
                  left: 175,
                  right: 175,
                  child: Text(
                    'Проверьте данные пассажира №${logic.passengerNumber.value + 1}',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 64,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                );
              }),
              Positioned(
                top: 250,
                left: 50,
                right: 50,
                child: Column(
                  spacing: 70,
                  children: [
                    Row(
                      spacing: 16,
                      children: [
                        //Гражданство
                        Container(
                          width: 590,
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: TextField(
                                  decoration: InputDecoration(hintText: ""),
                                  cursorColor: Colors.black,
                                  cursorHeight: 64,
                                  cursorWidth: 2,
                                  cursorRadius: Radius.zero,
                                  onTapOutside: (event) {
                                    FocusScope.of(context).requestFocus(logic.citizenshipFocusNode);
                                  },
                                  showCursor: false,
                                  focusNode: logic.citizenshipFocusNode,
                                  controller: logic.citizenshipController,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 64,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Text(
                                "Гражданство",
                                style: TextStyle(
                                  color: Color(0xFFC5CCD4),
                                  fontSize: 36,
                                  fontWeight: FontWeight.w100,
                                ),
                              )
                            ],
                          ),
                        ),

                        //Тип документа
                        Container(
                          width: 590,
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: TextField(
                                  enabled: true,
                                  decoration: InputDecoration(hintText: ""),
                                  cursorColor: Colors.black,
                                  cursorHeight: 64,
                                  cursorWidth: 2,
                                  cursorRadius: Radius.zero,
                                  onTapOutside: (event) {
                                    FocusScope.of(context).requestFocus(logic.documentTypeFocusNode);
                                  },
                                  showCursor: false,
                                  focusNode: logic.documentTypeFocusNode,
                                  controller: logic.documentTypeController,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 64,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Text(
                                "Тип документа",
                                style: TextStyle(
                                  color: Color(0xFFC5CCD4),
                                  fontSize: 36,
                                  fontWeight: FontWeight.w100,
                                ),
                              )
                            ],
                          ),
                        ),

                        //Серия и номер документа
                        Container(
                          width: 590,
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: TextFormField(
                                  enabled: true,
                                  validator: logic.validateDocumentNumber,
                                  decoration: InputDecoration(hintText: ""),
                                  cursorColor: Colors.black,
                                  cursorHeight: 64,
                                  cursorWidth: 2,
                                  cursorRadius: Radius.zero,
                                  onTapOutside: (event) {
                                    FocusScope.of(context).requestFocus(logic.documentNumberEditFocusNode);
                                  },
                                  showCursor: true,
                                  focusNode: logic.documentNumberEditFocusNode,
                                  controller: logic.seraiNumberController,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 64,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Text(
                                "Серия и номер документа",
                                style: TextStyle(
                                  color: Color(0xFFC5CCD4),
                                  fontSize: 36,
                                  fontWeight: FontWeight.w100,
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                    Row(
                      spacing: 16,
                      children: [
                        //Фамилия
                        Container(
                          width: 590,
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: TextFormField(
                                  enabled: true,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Поле не может быть пустым';
                                    }
                                    return null;
                                  },
                                  decoration: InputDecoration(hintText: ""),
                                  cursorColor: Colors.black,
                                  cursorHeight: 64,
                                  cursorWidth: 2,
                                  cursorRadius: Radius.zero,
                                  onTapOutside: (event) {
                                    FocusScope.of(context).requestFocus(logic.lastNameEditFocusNode);
                                  },
                                  showCursor: false,
                                  focusNode: logic.lastNameEditFocusNode,
                                  controller: logic.lastNameController,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 64,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Text(
                                "Фамилия",
                                style: TextStyle(
                                  color: Color(0xFFC5CCD4),
                                  fontSize: 36,
                                  fontWeight: FontWeight.w100,
                                ),
                              )
                            ],
                          ),
                        ),

                        //Имя
                        Container(
                          width: 590,
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: TextFormField(
                                  enabled: true,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Поле не может быть пустым';
                                    }
                                    return null;
                                  },
                                  decoration: InputDecoration(hintText: ""),
                                  cursorColor: Colors.black,
                                  cursorHeight: 64,
                                  cursorWidth: 2,
                                  cursorRadius: Radius.zero,
                                  onTapOutside: (event) {
                                    FocusScope.of(context).requestFocus(logic.nameEditFocusNode);
                                  },
                                  showCursor: false,
                                  focusNode: logic.nameEditFocusNode,
                                  controller: logic.nameController,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 64,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Text(
                                "Имя",
                                style: TextStyle(
                                  color: Color(0xFFC5CCD4),
                                  fontSize: 36,
                                  fontWeight: FontWeight.w100,
                                ),
                              )
                            ],
                          ),
                        ),

                        //Отчество
                        Container(
                          width: 590,
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: TextFormField(
                                  enabled: true,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Поле не может быть пустым';
                                    }
                                    return null;
                                  },
                                  decoration: InputDecoration(hintText: ""),
                                  cursorColor: Colors.black,
                                  cursorHeight: 64,
                                  cursorWidth: 2,
                                  cursorRadius: Radius.zero,
                                  onTapOutside: (event) {
                                    FocusScope.of(context).requestFocus(logic.secondNameEditFocusNode);
                                  },
                                  showCursor: false,
                                  focusNode: logic.secondNameEditFocusNode,
                                  controller: logic.secondNameController,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 64,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Text(
                                "Отчество",
                                style: TextStyle(
                                  color: Color(0xFFC5CCD4),
                                  fontSize: 36,
                                  fontWeight: FontWeight.w100,
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 16,
                      children: [
                        //Пол
                        Container(
                          width: 590,
                          // height: 160,
                          child: Column(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.center, children: [
                            InkWell(
                              onTap: () {
                                Get.toNamed('/passBirthDateScreen');
                              },
                              child: Text(
                                logic.isMale.value ? "мужской" : "женский",
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 64,
                                  fontFamily: 'Overpass',
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 6,
                            ),
                            Container(
                              width: 590,
                              height: 1,
                              color: Colors.black54,
                            ),
                            Text(
                              "Пол",
                              style: TextStyle(
                                color: Color(0xFFC5CCD4),
                                fontSize: 36,
                                fontWeight: FontWeight.w100,
                              ),
                            )
                          ]),
                        ),

                        //Дата рождения
                        Container(
                          width: 590,
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: TextFormField(
                                  enabled: true,
                                  validator: logic.validateBirthDate,
                                  decoration: InputDecoration(hintText: ""),
                                  cursorColor: Colors.black,
                                  cursorHeight: 64,
                                  cursorWidth: 2,
                                  cursorRadius: Radius.zero,
                                  onTapOutside: (event) {
                                    FocusScope.of(context).requestFocus(logic.birthDateEditFocusNode);
                                  },
                                  showCursor: false,
                                  focusNode: logic.birthDateEditFocusNode,
                                  controller: logic.birthDateController,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 64,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Text(
                                "Дата рождения",
                                style: TextStyle(
                                  color: Color(0xFFC5CCD4),
                                  fontSize: 36,
                                  fontWeight: FontWeight.w100,
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              backMainButton(),
              nextButton(),
            ],
          ),
        ),
      ),
    );
  }

  backMainButton() {
    return Positioned(
      left: 68,
      bottom: 46,
      child: MainBackButton(),
    );
  }

  nextButton() {
    return Positioned(
      bottom: 46,
      right: 61,
      child: Opacity(
        opacity: 1,
        child: CustomInkWell(
          color: AppColors.primary,
          splashColor: AppColors.primarySplashColor,
          borderRadius: 12,
          onTap: () {
            if (!_formKey.currentState!.validate()) {
              return;
            }
            if (logic.passengerNumber.value == globals.tripOrder.passengersList.length - 1) {
              // последний пассажир
              logic.savePassenger();
              if ((globals.tripOrder.selectedTripDetail.value.noticeInfo?.noticeAvailable ?? false)) {
                //globals.tripOrder.noticeInfoMessage.value = true;
                globals.tripOrder.noticeInfoMessage.value = (globals.tripOrder.selectedTripDetail.value.noticeInfo?.useNoticeDefault ?? true);
                Get.toNamed('/passContactsScreen');
              } else {
                Get.toNamed('/orderCheckScreen');
              }
            } else {
              log("Переходим на следующий пассажир");
              logic.passengerNumber.value++;
              Get.toNamed('/documentScreen');
            }
          },
          child: Container(
            width: 180,
            height: 82,
            padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
            clipBehavior: Clip.antiAlias,
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Далее',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.w900,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
