import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import '../helpers.dart';
import 'pass_manual_logic.dart';

class PassNameScreen extends StatelessWidget {
  PassNameScreen({Key? key}) : super(key: key);

  final logic = Get.put(PassManualLogic());
  final currentScreen = PassengerScreen.NameScreen.obs;
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey,
      body: Form(
        key: _formKey,
        child: Container(
          width: 1920,
          height: 1080,
          color: Colors.white,
          child: Stack(
            children: [
              BackOrMainButton(),
              Obx(() {
                return Positioned(
                  top: 80,
                  left: 175,
                  right: 175,
                  child: Text(
                    'Введите данные пассажира №${logic.passengerNumber.value + 1}',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 64,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                );
              }),

              //Фамилия
              Positioned(
                top: 250,
                left: 60,
                child: Container(
                  width: 590,
                  height: 150,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: TextFormField(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Поле не может быть пустым';
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                            hintText: "",
                            hintStyle: TextStyle(color: Colors.grey),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppColors.primary, width: 2),
                            ),
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppColors.enabledBorder, width: 2),
                            ),
                          ),
                          cursorColor: Colors.black,
                          cursorHeight: 64,
                          cursorWidth: 2,
                          cursorRadius: Radius.zero,
                          onTapOutside: (event) {
                            FocusScope.of(context).requestFocus(logic.lastNameFocusNode);
                          },
                          showCursor: true,
                          focusNode: logic.lastNameFocusNode,
                          controller: logic.lastNameController,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 64,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Obx(() {
                        return Text(
                          "Фамилия",
                          style: TextStyle(
                            color: logic.isLastNameFocused.value ? AppColors.primary : AppColors.enabledBorder,
                            fontSize: 36,
                            fontWeight: FontWeight.w100,
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),

              //Имя
              Positioned(
                top: 250,
                left: 665,
                child: Container(
                  width: 590,
                  height: 150,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: TextFormField(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Поле не может быть пустым';
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                            hintText: "",
                            hintStyle: TextStyle(color: Colors.grey),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppColors.primary, width: 2),
                            ),
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppColors.enabledBorder, width: 2),
                            ),
                          ),
                          cursorColor: Colors.black,
                          cursorHeight: 64,
                          cursorWidth: 2,
                          cursorRadius: Radius.zero,
                          onTapOutside: (event) {
                            FocusScope.of(context).requestFocus(logic.nameFocusNode);
                          },
                          showCursor: true,
                          focusNode: logic.nameFocusNode,
                          controller: logic.nameController,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 64,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Obx(() {
                        return Text(
                          "Имя",
                          style: TextStyle(
                            color: logic.isNameFocused.value ? AppColors.primary : AppColors.enabledBorder,
                            fontSize: 36,
                            fontWeight: FontWeight.w100,
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),

              //Отчество
              Positioned(
                top: 250,
                left: 1271,
                child: Container(
                  width: 590,
                  height: 150,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: TextFormField(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Поле не может быть пустым';
                            }
                            return null;
                          },
                          decoration: InputDecoration(
                            hintText: "",
                            hintStyle: TextStyle(color: Colors.grey),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppColors.primary, width: 2),
                            ),
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: AppColors.enabledBorder, width: 2),
                            ),
                          ),
                          cursorColor: Colors.black,
                          cursorHeight: 64,
                          cursorWidth: 2,
                          cursorRadius: Radius.zero,
                          onTapOutside: (event) {
                            FocusScope.of(context).requestFocus(logic.secondNameFocusNode);
                          },
                          showCursor: true,
                          focusNode: logic.secondNameFocusNode,
                          controller: logic.secondNameController,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 64,
                            fontWeight: FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Obx(() {
                        return Text(
                          "Отчество",
                          style: TextStyle(
                            color: logic.isSecondNameFocused.value ? AppColors.primary : AppColors.enabledBorder,
                            fontSize: 36,
                            fontWeight: FontWeight.w100,
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),
              backMainButton(),
              nextButton(),
              Obx(() {
                return Positioned(bottom: 0, left: 0, right: 0, child: Keyboard(logic.currentTextController.value));
              })
            ],
          ),
        ),
      ),
    );
  }

  backMainButton() {
    return Positioned(
      left: 68,
      top: 552,
      child: MainBackButton(),
    );
  }

  nextButton() {
    return Positioned(
      bottom: 446,
      right: 61,
      child: Opacity(
        opacity: 1,
        child: CustomInkWell(
          color: AppColors.primary,
          splashColor: AppColors.primarySplashColor,
          borderRadius: 12,
          onTap: () {
            if (_formKey.currentState!.validate()) {
              if (Get.arguments?['isEdit'] == true) {
                Get.toNamed('/passCheckScreen');
              } else {
                Get.toNamed('/passBirthDateScreen');
              }
            }
          },
          child: Container(
            width: 180,
            height: 82,
            padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
            clipBehavior: Clip.antiAlias,
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Далее',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.w900,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
