import 'dart:developer';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:terminal/models/qrstatus_response.dart';
import '../../src/api.dart';
import '../../src/globals.dart';

enum PassengerScreen { OneFieldScreen, PassportScreen, NameScreen, BirthDateScreen, AllDataScreen, ContactsScreen, CitizenShipSearchScreen, DocumentTypeScreen }

class RomanNumeralTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    final romanNumerals = RegExp(r'^[IVXLCDM]+$');
    if (romanNumerals.hasMatch(newValue.text)) {
      return newValue;
    }
    return oldValue;
  }
}

class PassManualLogic extends GetxController {
  final api = Get.find<Api>();
  final globals = Get.find<Globals>();

  TextEditingController lastNameController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController secondNameController = TextEditingController();
  TextEditingController citizenshipController = TextEditingController();
  TextEditingController documentTypeController = TextEditingController();
  TextEditingController seraiNumberController = TextEditingController();
  TextEditingController birthDateController = TextEditingController();
  //TextEditingController emailController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  final isMale = true.obs;

  FocusNode lastNameFocusNode = FocusNode();
  FocusNode nameFocusNode = FocusNode();
  FocusNode secondNameFocusNode = FocusNode();
  FocusNode citizenshipFocusNode = FocusNode();
  FocusNode documentTypeFocusNode = FocusNode();
  FocusNode documentNumberFocusNode = FocusNode();
  FocusNode birthDateFocusNode = FocusNode();
  //FocusNode emailFocusNode = FocusNode();
  FocusNode phoneFocusNode = FocusNode();

  /// Экран подтверждения данных
  FocusNode documentNumberEditFocusNode = FocusNode();
  FocusNode lastNameEditFocusNode = FocusNode();
  FocusNode nameEditFocusNode = FocusNode();
  FocusNode secondNameEditFocusNode = FocusNode();
  FocusNode birthDateEditFocusNode = FocusNode();

  final currentTextController = TextEditingController().obs;
  final currentFocusNode = FocusNode().obs;
  final currentFieldName = "".obs;

  int previousPassengerNumber = 0;
  final passengerNumber = 0.obs;

  var maskPassFormatter = MaskTextInputFormatter(
      mask: 'AAA-##№#######',
      filter: {
        "A": RegExp(r'[IVXLCDM]'), // Roman numerals
        "#": RegExp(r'[0-9]'), // Digits
      },
      type: MaskAutoCompletionType.lazy);

  final birthDateMaskFormatter = MaskTextInputFormatter(
    mask: '##.##.####',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy,
  );

  final phoneMaskFormatter = MaskTextInputFormatter(
    mask: '+7 (###) ###-##-##',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy,
  );

  /// Флаги фокуса
  final isLastNameFocused = false.obs;
  final isNameFocused = false.obs;
  final isSecondNameFocused = false.obs;
  final isCitizenshipFocused = false.obs;
  final isDocumentTypeFocused = false.obs;
  final isDocumentNumberFocused = false.obs;
  final isBirthDateFocused = false.obs;
  //final isEmailFocused = false.obs;
  final isPhoneFocused = false.obs;

  String? validateDocumentNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Поле не может быть пустым';
    }
    final selectedDocument = globals.docTypeList.firstWhereOrNull((val) => val.id == globals.tripOrder.selectedDocumentTypeId.value);
    if (selectedDocument?.mask != null && !RegExp(selectedDocument!.mask!).hasMatch(value)) {
      return 'Формат: ${selectedDocument.maskDescription}';
    }
    return null;
  }

  String? validateBirthDate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Поле не может быть пустым';
    }

    // Регулярное выражение для формата dd.MM.yyyy
    final regex = RegExp(r'^\d{2}\.\d{2}\.\d{4}$');
    if (!regex.hasMatch(value)) {
      return 'Введите дату в формате ДД.ММ.ГГГГ';
    }

    // Парсим дату
    try {
      final parts = value.split('.');
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);

      final birthDate = DateTime(year, month, day);
      final now = DateTime.now();

      if (year < 1900) {
        return 'Год рождения некорректен';
      }

      if (month > 12) {
        return 'Введите корректный месяц';
      }

      if (day > 31) {
        return 'Введите корректный день';
      }

      if (birthDate.isAfter(now)) {
        return 'Дата не может быть в будущем';
      }
    } catch (e) {
      return 'Введите корректную дату';
    }

    return null;
  }

  @override
  void onInit() {
    globals.tripOrder.passengersList = globals.tripOrder.lkPassengersList.where((p) => p.isSelected.value).toList(); // копируем выбранных пассажиров
    passengerNumber.value = globals.tripOrder.passengersList.length; // Стартовый номер пассажира для заполнения

    final _allPass = globals.tripOrder.adultsCount.value + globals.tripOrder.childrenCount.value;
    for (var i = globals.tripOrder.passengersList.length; i < _allPass; i++) {
      // добавляем пустых пассажиров
      globals.tripOrder.passengersList.add(Passenger(
        country: globals.countriesList.firstWhereOrNull((val) => val.id == globals.tripOrder.selectedCitizenshipId),
        typeDocument: globals.docTypeList.firstWhereOrNull((val) => val.id == globals.tripOrder.selectedDocumentTypeId.value),
      ));
    }

    passengerNumber.listen((no) {
      log("Пассажир ${no} - prev: ${previousPassengerNumber}");

      if (no == previousPassengerNumber) return;
      log(no > previousPassengerNumber ? "следующий пассажир" : "предыдущий пассажир");
      savePassengerNo(previousPassengerNumber);
      getPassengerNo(no);
      previousPassengerNumber = no;
    });

    citizenshipController.text = globals.countriesList.firstWhereOrNull((val) => val.id == globals.tripOrder.selectedCitizenshipId)?.title ?? "";
    documentTypeController.text = globals.docTypeList.firstWhereOrNull((val) => val.id == globals.tripOrder.selectedDocumentTypeId.value)?.title ?? "";

    seraiNumberController.value = TextEditingValue(text: '');

    // Устанавливаем фокус на поля по умолчанию
    WidgetsBinding.instance.addPostFrameCallback((_) {
      documentNumberFocusNode.requestFocus();
      lastNameFocusNode.requestFocus();
      birthDateFocusNode.requestFocus();
    });

    /// [Подтверждение экран] Номер документа
    documentNumberEditFocusNode.addListener(() async {
      documentNumberEditFocusNode.unfocus();
      await Get.toNamed("/passportScreen", arguments: {'isEdit': true});
      Future.delayed(Duration(milliseconds: 100), () {
        documentNumberFocusNode.requestFocus();
      });
    });

    /// [Подтверждение экран] Фамилия
    lastNameEditFocusNode.addListener(() async {
      lastNameEditFocusNode.unfocus();
      await Get.toNamed("/passNameScreen", arguments: {'isEdit': true});
      Future.delayed(Duration(milliseconds: 100), () {
        lastNameFocusNode.requestFocus();
      });
    });

    /// [Подтверждение экран] Имя
    nameEditFocusNode.addListener(() async {
      nameEditFocusNode.unfocus();
      await Get.toNamed("/passNameScreen", arguments: {'isEdit': true});
      Future.delayed(Duration(milliseconds: 100), () {
        nameFocusNode.requestFocus();
      });
    });

    /// [Подтверждение экран] Отчество
    secondNameEditFocusNode.addListener(() async {
      secondNameEditFocusNode.unfocus();
      await Get.toNamed("/passNameScreen", arguments: {'isEdit': true});
      Future.delayed(Duration(milliseconds: 100), () {
        secondNameFocusNode.requestFocus();
      });
    });

    /// [Подтверждение экран] Дата рождения
    birthDateEditFocusNode.addListener(() async {
      birthDateEditFocusNode.unfocus();
      await Get.toNamed("/passBirthDateScreen", arguments: {'isEdit': true});
      Future.delayed(Duration(milliseconds: 100), () {
        birthDateFocusNode.requestFocus();
      });
    });

    /// Фамилия
    lastNameFocusNode.addListener(() {
      isLastNameFocused.value = lastNameFocusNode.hasFocus;
      currentTextController.value = lastNameController;
      currentFocusNode.value = lastNameFocusNode;
    });

    /// Имя
    nameFocusNode.addListener(() {
      isNameFocused.value = nameFocusNode.hasFocus;
      currentTextController.value = nameController;
      currentFocusNode.value = nameFocusNode;
    });

    /// Отчество
    secondNameFocusNode.addListener(() {
      isSecondNameFocused.value = secondNameFocusNode.hasFocus;
      currentTextController.value = secondNameController;
      currentFocusNode.value = secondNameFocusNode;
    });

    /// Гражданство
    citizenshipFocusNode.addListener(() async {
      isCitizenshipFocused.value = citizenshipFocusNode.hasFocus;
      await Get.toNamed("/citizenshipSearchScreen");
      citizenshipController.text = globals.countriesList.firstWhereOrNull((val) => val.id == globals.tripOrder.selectedCitizenshipId)?.title ?? "n/a";
      documentNumberFocusNode.requestFocus();
    });

    /// Тип документа
    documentTypeFocusNode.addListener(() async {
      isDocumentTypeFocused.value = documentTypeFocusNode.hasFocus;
      await Get.toNamed("/docTypeSearchScreen");
      final newDocumentTypeTitle = globals.docTypeList.firstWhereOrNull((val) => val.id == globals.tripOrder.selectedDocumentTypeId.value)?.title ?? "n/a";
      //Есди изменили тип документа то очищаем поле номер документа
      if (documentTypeController.text != newDocumentTypeTitle) {
        seraiNumberController.clear();
      }
      documentTypeController.text = newDocumentTypeTitle;
      documentNumberFocusNode.requestFocus();
    });

    /// Номер документа
    documentNumberFocusNode.addListener(() {
      isDocumentNumberFocused.value = documentNumberFocusNode.hasFocus;
      currentTextController.value = seraiNumberController;
      currentFocusNode.value = documentNumberFocusNode;
    });

    /// Дата рождения
    birthDateController.addListener(() {
      final newText = birthDateMaskFormatter
          .formatEditUpdate(
            TextEditingValue.empty,
            TextEditingValue(text: birthDateController.text),
          )
          .text;
      if (birthDateController.text != newText) {
        birthDateController.value = TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(offset: newText.length),
        );
      }
    });
    birthDateFocusNode.addListener(() {
      isBirthDateFocused.value = birthDateFocusNode.hasFocus;
      currentTextController.value = birthDateController;
      currentFocusNode.value = birthDateFocusNode;
    });

    /// Email
    // emailController.addListener(() {
    //   final filteredText = emailController.text.replaceAll(RegExp(r'[^a-zA-Z0-9@._-]'), '');
    //   if (emailController.text != filteredText) {
    //     emailController.value = emailController.value.copyWith(
    //       text: filteredText,
    //       selection: TextSelection.collapsed(offset: filteredText.length),
    //     );
    //   }
    // });
    // emailFocusNode.addListener(() {
    //   isEmailFocused.value = emailFocusNode.hasFocus;
    //   Keyboard.setLanguage(emailFocusNode.hasFocus);
    //   currentTextController.value = emailController;
    //   currentFocusNode.value = emailFocusNode;
    // });

    /// Номер телефона
    phoneController.addListener(() {
      final newText = phoneMaskFormatter
          .formatEditUpdate(
            TextEditingValue.empty,
            TextEditingValue(text: phoneController.text),
          )
          .text;
      if (phoneController.text != newText) {
        phoneController.value = TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(offset: newText.length),
        );
      }
    });
    phoneFocusNode.addListener(() {
      isPhoneFocused.value = phoneFocusNode.hasFocus;
      currentTextController.value = phoneController;
      currentFocusNode.value = phoneFocusNode;
    });

    super.onInit();
  }

  savePassengerNo(int no) {
    globals.tripOrder.passengersList[no].lastName = lastNameController.text;
    globals.tripOrder.passengersList[no].firstName = nameController.text;
    globals.tripOrder.passengersList[no].secondName = secondNameController.text;
    globals.tripOrder.passengersList[no].citizenship = globals.countriesList.firstWhereOrNull((c) => c.id == globals.tripOrder.selectedCitizenshipId);
    globals.tripOrder.passengersList[no].typeDocument = globals.docTypeList.firstWhereOrNull((d) => d.id == globals.tripOrder.selectedDocumentTypeId.value);
    globals.tripOrder.passengersList[no].seriaNumber = seraiNumberController.text;
    globals.tripOrder.passengersList[no].dateOfBirthday = birthDateController.text;
    globals.tripOrder.passengersList[no].sex = isMale.value ? 1 : 0;
  }

  savePassenger() {
    savePassengerNo(passengerNumber.value);
  }

  getPassengerNo(int no) {
    lastNameController.text = globals.tripOrder.passengersList[no].lastName ?? "";
    nameController.text = globals.tripOrder.passengersList[no].firstName ?? "";
    secondNameController.text = globals.tripOrder.passengersList[no].secondName ?? "";
    citizenshipController.text = globals.tripOrder.passengersList[no].country?.title ?? "";
    documentTypeController.text = globals.tripOrder.passengersList[no].typeDocument?.title ?? "";
    seraiNumberController.text = globals.tripOrder.passengersList[no].seriaNumber ?? "";
    birthDateController.text = globals.tripOrder.passengersList[no].dateOfBirthday ?? "";
    isMale.value = globals.tripOrder.passengersList[no].sex == 1;
  }

  @override
  void onClose() {
    // TODO: implement onClose

    lastNameFocusNode.dispose();
    nameFocusNode.dispose();
    secondNameFocusNode.dispose();
    citizenshipFocusNode.dispose();
    documentTypeFocusNode.dispose();
    documentNumberFocusNode.dispose();
    birthDateFocusNode.dispose();
    //emailFocusNode.dispose();
    phoneFocusNode.dispose();

    documentNumberEditFocusNode.dispose();
    lastNameEditFocusNode.dispose();
    nameEditFocusNode.dispose();
    secondNameEditFocusNode.dispose();
    birthDateEditFocusNode.dispose();

    lastNameController.dispose();
    nameController.dispose();
    secondNameController.dispose();
    citizenshipController.dispose();
    documentTypeController.dispose();
    seraiNumberController.dispose();
    birthDateController.dispose();
    //emailController.dispose();
    phoneController.dispose();

    super.onClose();
  }
}
