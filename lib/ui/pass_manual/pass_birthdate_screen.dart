import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import '../helpers.dart';
import 'pass_manual_logic.dart';

class PassBirthDateScreen extends StatelessWidget {
  PassBirthDateScreen({Key? key}) : super(key: key);

  final logic = Get.put(PassManualLogic());
  final currentScreen = PassengerScreen.PassportScreen.obs;
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey,
      body: Form(
        key: _formKey,
        child: Container(
          width: 1920,
          height: 1080,
          color: Colors.white,
          child: Stack(
            children: [
              BackOrMainButton(),
              Positioned(
                  top: 80,
                  left: 175,
                  right: 175,
                  child: Text(
                    'Введите данные пассажира №${logic.passengerNumber.value + 1}',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 64,
                      fontWeight: FontWeight.w800,
                    ),
                  )),
              Obx(
                () {
                  return Positioned(
                    top: 250,
                    left: 60,
                    right: 60,
                    child: Row(
                      spacing: 20,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 590,
                          // height: 170,
                          child: Column(
                            spacing: 20,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(top: 5.0),
                                child: Row(
                                  spacing: 20,
                                  children: [
                                    ChoiceChip(
                                      showCheckmark: false,
                                      padding: const EdgeInsets.symmetric(vertical: 1.0, horizontal: 12.0),
                                      shape: logic.isMale.value
                                          ? RoundedRectangleBorder(
                                              side: BorderSide(width: 3, color: Color(0xFF1E90FF)),
                                              borderRadius: BorderRadius.circular(12),
                                            )
                                          : RoundedRectangleBorder(
                                              side: BorderSide(width: 0, color: Colors.transparent),
                                            ),
                                      label: Text(
                                        'мужской',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color: !logic.isMale.value ? Colors.black : Color(0xFF1E90FF),
                                          fontSize: 48,
                                          fontFamily: 'Overpass',
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      backgroundColor: Colors.white,
                                      color: WidgetStatePropertyAll(Colors.white),
                                      selected: logic.isMale.value,
                                      onSelected: (value) {
                                        if (value) {
                                          logic.isMale.value = true;
                                        }
                                      },
                                    ),
                                    ChoiceChip(
                                      showCheckmark: false,
                                      padding: const EdgeInsets.symmetric(vertical: 1.0, horizontal: 12.0),
                                      shape: !logic.isMale.value
                                          ? RoundedRectangleBorder(
                                              side: BorderSide(width: 3, color: Color(0xFF1E90FF)),
                                              borderRadius: BorderRadius.circular(12),
                                            )
                                          : RoundedRectangleBorder(
                                              side: BorderSide(width: 0, color: Colors.transparent),
                                            ),
                                      backgroundColor: Colors.white,
                                      color: WidgetStatePropertyAll(Colors.white),
                                      label: Text(
                                        'женский',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color: logic.isMale.value ? Colors.black : Color(0xFF1E90FF),
                                          fontSize: 48,
                                          fontFamily: 'Overpass',
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      selected: !logic.isMale.value,
                                      onSelected: (value) {
                                        if (value) {
                                          logic.isMale.value = false;
                                        }
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                width: 590,
                                height: 1,
                                color: Colors.black,
                              ),
                              Text(
                                "Пол",
                                style: TextStyle(
                                  color: Color(0xFFC5CCD4),
                                  fontSize: 36,
                                  fontWeight: FontWeight.w100,
                                ),
                              )
                            ],
                          ),
                        ),
                        Container(
                          width: 590,
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: TextFormField(
                                  validator: logic.validateBirthDate,
                                  decoration: InputDecoration(
                                    hintText: "03.11.1989",
                                    hintStyle: TextStyle(color: Colors.grey),
                                    focusedBorder: UnderlineInputBorder(
                                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                                    ),
                                    enabledBorder: UnderlineInputBorder(
                                      borderSide: BorderSide(color: AppColors.enabledBorder, width: 2),
                                    ),
                                  ),
                                  cursorColor: Colors.black,
                                  cursorHeight: 64,
                                  cursorWidth: 2,
                                  cursorRadius: Radius.zero,
                                  onTapOutside: (event) {
                                    FocusScope.of(context).requestFocus(logic.birthDateFocusNode);
                                  },
                                  showCursor: true,
                                  focusNode: logic.birthDateFocusNode,
                                  controller: logic.birthDateController,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 64,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Obx(() {
                                return Text(
                                  "Дата рождения",
                                  style: TextStyle(
                                    color: logic.isBirthDateFocused.value ? AppColors.primary : AppColors.enabledBorder,
                                    fontSize: 36,
                                    fontWeight: FontWeight.w100,
                                  ),
                                );
                              }),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              backMainButton(),
              nextButton(),
              Obx(
                () {
                  return Positioned(bottom: 0, left: 0, right: 0, child: Keyboard(logic.currentTextController.value));
                },
              )
            ],
          ),
        ),
      ),
    );
  }

  backMainButton() {
    return Positioned(
      left: 68,
      top: 552,
      child: MainBackButton(),
    );
  }

  nextButton() {
    return Positioned(
      bottom: 446,
      right: 61,
      child: Opacity(
        opacity: 1,
        child: CustomInkWell(
          color: AppColors.primary,
          splashColor: AppColors.primarySplashColor,
          borderRadius: 12,
          onTap: () {
            if (_formKey.currentState!.validate()) {
              Get.toNamed('/passCheckScreen');
            }
          },
          child: Container(
            width: 180,
            height: 82,
            padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
            clipBehavior: Clip.antiAlias,
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Далее',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.w900,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
