import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/models/qrstatus_response.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';
import 'package:terminal/ui/select_seats.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import '../../src/globals.dart';
import 'select_trip_details_logic.dart';

class SelectTripDetailsScreen extends StatelessWidget {
  SelectTripDetailsScreen({Key? key}) : super(key: key);

  final logic = Get.put(SelectTripDetailLogic());
  final globals = Get.find<Globals>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: 1920,
        height: 1080,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(color: Colors.white),
        child: Stack(
          children: [
            BackOrMainButton(),
            Positioned(
              left: 70,
              bottom: 57,
              child: MainBackButton(),
            ),
            selectSeats(),
            seatsBlock(),
            nextButton(),
          ],
        ),
      ),
    );
  }

  Stack baggageCountBlock() {
    return Stack(
      children: [
        Positioned(
          left: 193,
          top: 44,
          child: Container(
            width: 128,
            height: 56,
            padding: EdgeInsets.only(top: 5),
            decoration: ShapeDecoration(
              color: Color(0xFFF1F8FF),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: Center(
              child: Obx(() {
                return Text(globals.tripOrder.baggageCount.toString(),
                    textAlign: TextAlign.center, style: TextStyle(color: Colors.black, fontSize: 32, fontWeight: FontWeight.w700));
              }),
            ),
          ),
        ),
        Positioned(
          left: 121,
          top: 44,
          child: InkWell(
            onTap: () {
              if (globals.tripOrder.baggageCount.value > 0) globals.tripOrder.baggageCount.value--;
            },
            child: Container(
              width: 56,
              height: 56,
              padding: EdgeInsets.only(top: 5),
              decoration: ShapeDecoration(
                color: Color(0xFFF1F8FF),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Center(child: Text("-", textAlign: TextAlign.center, style: TextStyle(color: Colors.black, fontSize: 32, fontWeight: FontWeight.w700))),
            ),
          ),
        ),
        Positioned(
          left: 337,
          top: 44,
          child: InkWell(
            onTap: () {
              if (globals.tripOrder.baggageCount.value < 10) globals.tripOrder.baggageCount.value++;
            },
            child: Container(
              width: 56,
              height: 56,
              padding: EdgeInsets.only(top: 5),
              decoration: ShapeDecoration(
                color: Color(0xFFF1F8FF),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Center(child: Text("+", textAlign: TextAlign.center, style: TextStyle(color: Colors.black, fontSize: 32, fontWeight: FontWeight.w700))),
            ),
          ),
        ),
        Positioned(
          left: 140.71,
          top: 0,
          child: Text(
            'Количество багажа',
            style: TextStyle(
              color: Colors.black,
              fontSize: 24,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ],
    );
  }

  Widget adultsCountBlock() {
    return Column(
      spacing: 16,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Количество взрослых билетов',
          style: TextStyle(
            color: Colors.black,
            fontSize: 24,
            fontWeight: FontWeight.w700,
          ),
        ),
        Row(
          spacing: 16,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            InkWell(
              onTap: () {
                if (globals.tripOrder.adultsCount.value > 0) globals.tripOrder.adultsCount.value--;
              },
              child: Container(
                width: 56,
                height: 56,
                padding: EdgeInsets.only(top: 5),
                decoration: ShapeDecoration(
                  color: Color(0xFFF1F8FF),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Center(child: Text("-", textAlign: TextAlign.center, style: TextStyle(color: Colors.black, fontSize: 32, fontWeight: FontWeight.w700))),
              ),
            ),
            Container(
              width: 128,
              height: 56,
              padding: EdgeInsets.only(top: 5),
              decoration: ShapeDecoration(
                color: Color(0xFFF1F8FF),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Center(child: Obx(() {
                return Text(globals.tripOrder.adultsCount.toString(),
                    textAlign: TextAlign.center, style: TextStyle(color: Colors.black, fontSize: 32, fontWeight: FontWeight.w700));
              })),
            ),
            InkWell(
              onTap: () {
                if (globals.tripOrder.adultsCount.value < 10) globals.tripOrder.adultsCount.value++;
              },
              child: Container(
                width: 56,
                height: 56,
                padding: EdgeInsets.only(top: 5),
                decoration: ShapeDecoration(
                  color: Color(0xFFF1F8FF),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Center(child: Text("+", textAlign: TextAlign.center, style: TextStyle(color: Colors.black, fontSize: 32, fontWeight: FontWeight.w700))),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget childrenCountBlock() {
    if (globals.tripOrder.selectedTripDetail.value.priceChild == 0) return Container();

    return Column(
      spacing: 16,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Количество детских билетов',
          style: TextStyle(
            color: Colors.black,
            fontSize: 24,
            fontWeight: FontWeight.w700,
          ),
        ),
        Row(
          spacing: 16,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            InkWell(
              onTap: () {
                if (globals.tripOrder.childrenCount.value > 0) globals.tripOrder.childrenCount.value--;
              },
              child: Container(
                width: 56,
                height: 56,
                padding: EdgeInsets.only(top: 5),
                decoration: ShapeDecoration(
                  color: Color(0xFFF1F8FF),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Center(child: Text("-", textAlign: TextAlign.center, style: TextStyle(color: Colors.black, fontSize: 32, fontWeight: FontWeight.w700))),
              ),
            ),
            Container(
              width: 128,
              height: 56,
              padding: EdgeInsets.only(top: 5),
              decoration: ShapeDecoration(
                color: Color(0xFFF1F8FF),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Center(child: Obx(() {
                return Text(globals.tripOrder.childrenCount.toString(),
                    textAlign: TextAlign.center, style: TextStyle(color: Colors.black, fontSize: 32, fontWeight: FontWeight.w700));
              })),
            ),
            InkWell(
              onTap: () {
                if (globals.tripOrder.childrenCount.value < 10) globals.tripOrder.childrenCount.value++;
              },
              child: Container(
                width: 56,
                height: 56,
                padding: EdgeInsets.only(top: 5),
                decoration: ShapeDecoration(
                  color: Color(0xFFF1F8FF),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Center(child: Text("+", textAlign: TextAlign.center, style: TextStyle(color: Colors.black, fontSize: 32, fontWeight: FontWeight.w700))),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Positioned seatsBlock() {
    return Positioned(
      top: 60,
      right: 60,
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFF1F8FF),
          borderRadius: BorderRadius.circular(24),
          // border: Border.all(
          //   color: Colors.blue,
          // ),
        ),
        width: 590,
        height: 859,
        child: Stack(
          children: [
            orderHeader(),
            departureBlock(),
            arrivalBlock(),
            Obx(() {
              return Positioned(
                left: 52,
                top: 512,
                right: 52,
                height: 200,
                child: Column(
                  spacing: 8,
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (globals.tripOrder.adultsCount.value > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            globals.tripOrder.adultsCount.value.toString() + ' взрослый',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            (globals.tripOrder.adultsCount.value * (globals.tripOrder.selectedTripDetail.value.priceFull ?? 0).toDouble()).toString() + ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    if (globals.tripOrder.childrenCount.value > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            globals.tripOrder.childrenCount.value.toString() + ' детский',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            (globals.tripOrder.childrenCount.value * (globals.tripOrder.selectedTripDetail.value.priceChild ?? 0).toDouble()).toString() + ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    if (globals.tripOrder.baggageCount.value > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            globals.tripOrder.baggageCount.value.toString() + ' багажный',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            (globals.tripOrder.baggageCount.value.toDouble() * (globals.tripOrder.selectedTripDetail.value.priceBaggage ?? 0).toDouble())
                                    .toString() +
                                ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    if (globals.tripOrder.serviceFee > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Сервисный сбор автовокзала',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            globals.tripOrder.serviceFee.toString() + ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),

                    ///Комиссия за пассажирский билет
                    if (globals.tripOrder.commissionTicket > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Комиссия за пассажирский билет',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            globals.tripOrder.commissionTicket.toString() + ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                  ],
                ),
              );
            }),
            totalAmount(),
            dividerLine(),
            timeAndDetails(),
          ],
        ),
      ),
    );
  }

  Positioned orderHeader() {
    return Positioned(
      left: 127,
      top: 40,
      child: Text(
        'Ваш заказ',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Color(0xFF1E90FF),
          fontSize: 64,
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  Widget totalAmount() {
    return Obx(() {
      return Positioned(
        key: ValueKey(globals.tripOrder.selectedTripDetail.value.hashCode),
        left: 52,
        top: 761,
        right: 52,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'ИТОГО:',
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 36,
                fontWeight: FontWeight.w900,
              ),
            ),
            Text(
              globals.tripOrder.totalPrice.toString() + ' ₽',
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 36,
                fontWeight: FontWeight.w800,
              ),
            ),
          ],
        ),
      );
    });
  }

  Positioned dividerLine() {
    return Positioned(
        left: 52,
        top: 157,
        right: 52,
        child: CustomPaint(
          size: Size(482, 3), // Размер линии
          painter: DottedLinePainter(),
        ));
  }

  Positioned timeAndDetails() {
    return Positioned(
      left: 52,
      top: 368,
      right: 0,
      child: Container(
        // width: 289,
        height: 91,
        child: Obx(() {
          return Stack(
            children: [
              Positioned(
                left: 0,
                top: 0,
                child: Opacity(
                  opacity: 0.80,
                  child: Text(
                    'Время в пути: ${globals.tripOrder.selectedTripDetail.value.travelTimeHuman ?? ""}',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              Positioned(
                left: 0.29,
                top: 33,
                child: Opacity(
                  opacity: 0.80,
                  child: Text(
                    'Перевозчик: ${globals.tripOrder.selectedTripDetail.value.carrier ?? ""}',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              Positioned(
                left: 0,
                top: 66,
                child: Opacity(
                  opacity: 0.80,
                  child: Text(
                    'Автобус: ${globals.tripOrder.selectedTripDetail.value.busInfo ?? ""}',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Positioned arrivalBlock() {
    return Positioned(
      right: 52,
      top: 193,
      child: Obx(() {
        return Container(
          width: 297,
          height: 132,
          child: Stack(
            children: [
              Positioned(
                right: 0,
                top: 37,
                child: Text(
                  globals.tripOrder.endTimeString,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 48,
                    fontWeight: FontWeight.w800,
                  ),
                ),
              ),
              Positioned(
                left: 177,
                top: 0,
                child: Text(
                  'Прибытие',
                  style: TextStyle(
                    color: Color(0xFF1E90FF),
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              Positioned(
                right: 0,
                top: 102,
                child: Text(
                  "${globals.tripOrder.selectedTripDetail.value.pointB?.title ?? ""}",
                  style: TextStyle(
                    color: Color(0xFF3E3B56),
                    fontSize: 24,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  nextButton() {
    return Obx(() {
      return Positioned(
        bottom: 57,
        right: 60,
        child: Opacity(
            opacity: (globals.tripOrder.childrenCount.value + globals.tripOrder.adultsCount.value > 0) ? 1 : 0.30,
            child: CustomInkWell(
              color: AppColors.primary,
              splashColor: AppColors.primarySplashColor,
              borderRadius: 12,
              onTap: (globals.tripOrder.childrenCount.value + globals.tripOrder.adultsCount.value == 0)
                  ? () {}
                  : () {
                      //TODO: - Использовать только для тестирования
                      //globals.tripOrder.selectedTripDetail.value.withoutPd = false;

                      if (globals.tripOrder.selectedTripDetail.value.withoutPd ?? false) {
                        // Если не нужно выбирать места
                        globals.tripOrder.passengersList = List<Passenger>.generate(globals.tripOrder.childrenCount.value + globals.tripOrder.adultsCount.value,
                            (index) => Passenger(lastName: "Пассажир ${index + 1}"));

                        // Если нужно ввсести контакты
                        if ((globals.tripOrder.selectedTripDetail.value.noticeInfo?.noticeAvailable ?? false)) {
                          //globals.tripOrder.noticeInfoMessage.value = true;
                          globals.tripOrder.noticeInfoMessage.value = (globals.tripOrder.selectedTripDetail.value.noticeInfo?.useNoticeDefault ?? true);
                          Get.toNamed('/passContactsScreen');
                        } else {
                          Get.toNamed('/orderCheckScreen');
                        }
                      } else {
                        Get.toNamed('/passInfoScreen');
                      }
                    },
              child: Container(
                width: 180,
                height: 82,
                padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Далее',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 36,
                        fontWeight: FontWeight.w900,
                      ),
                    ),
                  ],
                ),
              ),
            )),
      );
    });
  }

  Positioned departureBlock() {
    return Positioned(
      left: 52,
      top: 193,
      child: Obx(() {
        return Container(
          width: 258.29,
          height: 132,
          child: Stack(
            children: [
              Positioned(
                left: 0,
                top: 37,
                child: Text(
                  globals.tripOrder.startTimeString,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 48,
                    fontWeight: FontWeight.w800,
                  ),
                ),
              ),
              Positioned(
                left: 0,
                top: 102,
                child: Text(
                  globals.tripOrder.selectedTripDetail.value.pointA?.title ?? "",
                  style: TextStyle(
                    color: Color(0xFF3E3B56),
                    fontSize: 24,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              Positioned(
                left: 0,
                top: 0,
                child: Text(
                  'Отправление',
                  style: TextStyle(
                    color: Color(0xFF1E90FF),
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget selectSeats() {
    return Stack(
      children: [
        Positioned(
          top: 80,
          left: 368,
          child: Column(
            children: [
              Text('Выберите места',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 64,
                    fontWeight: FontWeight.w900,
                  )),
              if (!(globals.tripOrder.selectedTripDetail.value.withoutSelectPlaces ?? false))
                Text(
                  'Схема мест приблизительная',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                  ),
                ),
            ],
          ),
        ),
        if (!(globals.tripOrder.selectedTripDetail.value.withoutSelectPlaces ?? false))
          Obx(() {
            // log("UPDATED") ;
            if (globals.dataState.value == DataLoadingState.LOADING || globals.tripOrder.selectedTripDetail.value.tripId == null) {
              return Positioned(
                top: 383,
                left: 668,
                child: CircularProgressIndicator(),
              );
            } else
              return Positioned(
                top: 280,
                left: 238,
                child: SeatWidget(
                  seatsSchema: globals.tripOrder.selectedTripDetail.value.seatsSchema ?? {},
                  freePlaces: globals.tripOrder.selectedTripDetail.value.freePlaces ?? [],
                  globals: globals,
                ),
              );
          }),
        if ((globals.tripOrder.selectedTripDetail.value.withoutSelectPlaces ?? false))
          Positioned(
              top: 261,
              left: 356,
              right: 1006,
              child: Column(spacing: 100, children: [
                adultsCountBlock(),
                childrenCountBlock(),
              ])),
      ],
    );
  }

  Widget noBaggageBlock() {
    return Container(
      // width: 597,
      height: 151,
      padding: EdgeInsets.all(40),
      decoration: ShapeDecoration(
        color: Color(0x331E90FF),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 44,
            height: 44,
            child: Image.asset("lib/assets/images/baggage.png"),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'К сожалению, перевозчик не предоставил',
                style: TextStyle(
                  color: Color(0xFF1E90FF),
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                'места для багажа на этом рейсе.',
                style: TextStyle(
                  color: Color(0xFF1E90FF),
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
