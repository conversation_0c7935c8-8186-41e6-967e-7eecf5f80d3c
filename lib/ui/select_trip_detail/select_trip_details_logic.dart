import 'dart:developer';
import 'package:get/get.dart';
import '../../models/trip_order_model.dart';
import '../../src/api.dart';
import '../../src/globals.dart';

class SelectTripDetailLogic extends GetxController {
  final api = Get.find<Api>();
  final globals = Get.find<Globals>();

  @override
  void onInit() {
    globals.loadPrefs();

    ever(globals.tripOrder.selectedSeats, (seats) {
      log("${seats.map((e) => "${e.type}/${e.seatNumber}")}");

      globals.tripOrder.adultsCount.value = seats.where((s) => s.type == SeatType.ADULT).length;
      globals.tripOrder.childrenCount.value = seats.where((s) => s.type == SeatType.CHILD).length;
    });

    super.onInit();
  }

  @override
  void onReady() async {
    api.getDocumentsTypes(globals.tripOrder.selectedTrip.value.pointA?.id ?? 0).then((val) {
      globals.docTypeList = val.results ?? [];
    });

    api.getCountries(globals.tripOrder.selectedTrip.value.pointA?.id ?? 0).then((val) {
      globals.countriesList = val.results ?? [];
    });

    final res = await api.getTripDetail(globals.tripOrder.selectedTrip.value.pointA?.id ?? 0, globals.tripOrder.selectedTrip.value.pointB?.id ?? 0,
        globals.tripOrder.selectedTrip.value.tripId ?? "", globals.tripOrder.selectedTrip.value.start ?? DateTime.now());

    if (res.tripId != null) {
      globals.tripOrder.selectedTripDetail.value = res;
    }

    super.onReady();
  }

  @override
  void onClose() {
    globals.tripOrder = TripOrder();

    super.onClose();
  }
}
