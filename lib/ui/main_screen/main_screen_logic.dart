import 'dart:async';
import 'package:get/get.dart';
import 'package:terminal/src/api.dart';
import 'package:terminal/src/globals.dart';

class MainScreenLogic extends GetxController {
  final globals = Get.find<Globals>();
  final api = Get.find<Api>();

  final timeString = ''.obs;
  final dateString = ''.obs;

  /// отображает время
  String get showTime {
    final time = DateTime.now();
    return "${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}";
  }

  String get showDate {
    final date = DateTime.now();
    final weekday = ['понедельник', 'вторник', 'среда', 'четверг', 'пятница', 'суббота', 'воскресенье'][date.weekday - 1];
    final month = ['января', 'февраля', 'марта', 'апреля', 'мая', 'июня', 'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря'][date.month - 1];
    final res = "$weekday, ${date.day} $month ${date.year}";
    return res;
  }

  updateTime() {
    timeString.value = showTime;
    dateString.value = showDate;
  }

  onInit() async {
    timeString.value = showTime;
    dateString.value = showDate;
    Timer.periodic(const Duration(seconds: 59), (timer) => updateTime());
    super.onInit();
  }
}
