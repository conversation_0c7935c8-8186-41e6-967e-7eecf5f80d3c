import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:terminal/models/stations_model.dart';
import 'package:terminal/ui/colors.dart';
import '../../src/globals.dart';
import 'main_screen_logic.dart';

class MainScreen extends StatelessWidget {
  final logic = Get.put(MainScreenLogic());
  final globals = Get.find<Globals>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: 1920,
        height: 1080,
        color: Colors.white,
        child: Obx(
          () {
            return Stack(
              children: [
                MainHeader(),
                BG(),
                BuyTicket(globals),
                PopularRoutes(globals),
                DateInfo(logic.dateString.value),
                TimeInfo(logic.timeString.value),
                CurrentStation(),
                BlackDonut(),
                Positioned(
                    top: 63,
                    left: 417,
                    height: 110,
                    child: Container(
                      color: Colors.black,
                      width: 0.1,
                    )),
                Logo(),
                // RightButton(
                //     globals: globals,
                //     title: 'Купить багаж',
                //     left: 1190,
                //     top: 272,
                //     onTap: () {
                //       print('Купить багажn');
                //     }),
                RightButton(
                    globals: globals,
                    title: 'Вернуть билет',
                    //left: 1190,
                    //top: 404,
                    left: 1190,
                    top: 536,
                    onTap: () {
                      Get.toNamed("/returnTicketScreen");
                    }),
                // RightButton(
                //     globals: globals,
                //     title: 'Напечатать дубликат',
                //     left: 1190,
                //     top: 536,
                //     onTap: () {
                //       print('Напечатать дубликат');
                //     }),
                RightButton(
                    globals: globals,
                    title: 'Нужна помощь?',
                    left: 1190,
                    top: 668,
                    onTap: () {
                      Get.toNamed("/supportScreen");
                    }),
                PointFrom(
                  globals: globals,
                  title: globals.currentStationConfig.value.defaultDispatch?.title ?? "",
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

class Logo extends StatelessWidget {
  const Logo({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 450,
      top: 94,
      child: Image.asset(
        'lib/assets/images/logo.png',
        width: 220,
      ),
    );
  }
}

class BG extends StatelessWidget {
  const BG({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 310,
      child: Image.asset("lib/assets/images/subtract.png", width: 1000),
    );
  }
}

class PointFrom extends StatelessWidget {
  PointFrom({required this.globals, required this.title});

  final String title;
  final Globals globals;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 81,
      top: 460,
      child: Container(
        width: 742,
        height: 300,
        child: Stack(
          children: [
            Positioned(
              left: 0,
              top: 19,
              child: Text(
                'Откуда:',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 56,
                  fontWeight: FontWeight.w900,
                ),
              ),
            ),
            Positioned(
              left: 254,
              top: 0,
              child: Obx(() {
                if (globals.dataState.value == DataLoadingState.LOADING) {
                  return ShimmerButton(
                    width: 488,
                    height: 108,
                    baseColor: AppColors.primary,
                    highlightColor: AppColors.shimmerPrimaryHighlight,
                    radius: 16,
                  );
                } else {
                  return AnimatedContainer(
                    width: 488,
                    height: 108,
                    duration: const Duration(seconds: 1),
                    curve: Curves.easeIn,
                    child: Material(
                      color: AppColors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(16),
                        splashColor: AppColors.primarySplashColor,
                        onTap: () {
                          Get.toNamed('searchFromScreen');
                        },
                        child: Container(
                          width: 488,
                          height: 108,
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(top: 12.0, right: 24),
                                  child: Text(
                                    title,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 56,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ),
                                Image.asset(
                                  'lib/assets/images/right.png',
                                  width: 24,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }
              }),
            ),
          ],
        ),
      ),
    );
  }
}

class RightButton extends StatelessWidget {
  const RightButton({
    super.key,
    required this.globals,
    required this.title,
    required this.left,
    required this.top,
    required this.onTap,
  });
  final Globals globals;
  final String title;
  final double left;
  final double top;
  final Function() onTap;

  @override
  Widget build(BuildContext context) {
    return Positioned(
        left: left,
        top: top,
        child: Obx(() {
          if (globals.dataState.value == DataLoadingState.LOADING) {
            return ShimmerButton(width: 650, height: 112, baseColor: AppColors.secondary);
          } else {
            return AnimatedContainer(
              width: 650,
              duration: const Duration(seconds: 1),
              curve: Curves.easeIn,
              child: Material(
                color: AppColors.secondary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(32),
                ),
                child: InkWell(
                  borderRadius: BorderRadius.circular(32),
                  splashColor: AppColors.secondary,
                  onTap: onTap,
                  child: Container(
                    width: 650,
                    height: 112,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(title,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: AppColors.primary,
                              fontSize: 36,
                              fontWeight: FontWeight.w700,
                            )),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }
        }));
  }
}

class BlackDonut extends StatelessWidget {
  const BlackDonut({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 80,
      top: 80,
      child: Container(
        width: 76,
        height: 76,
        decoration: ShapeDecoration(
          shape: OvalBorder(side: BorderSide(width: 20)),
        ),
      ),
    );
  }
}

class CurrentStation extends StatelessWidget {
  CurrentStation({
    super.key,
  });

  final globals = Get.find<Globals>();

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 175,
      top: 84,
      child: Container(
        width: 252,
        height: 69,
        child: Stack(
          children: [
            Positioned(
              left: 0,
              top: 0,
              child: Text(
                'Автовокзал',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 24,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
            Positioned(
              left: 0,
              top: 28,
              child: Text(
                globals.terminalConfig.value.defaultDispatch?.title ?? "",
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 32,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TimeInfo extends StatelessWidget {
  const TimeInfo(this.showTime);

  final String showTime;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 80,
      top: 121,
      child: Text(
        showTime,
        textAlign: TextAlign.right,
        style: TextStyle(
          color: Colors.black,
          fontSize: 64,
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }
}

class DateInfo extends StatelessWidget {
  const DateInfo(this.dateString);

  final String dateString;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 80,
      top: 80,
      child: Text(
        dateString,
        textAlign: TextAlign.right,
        style: TextStyle(
          color: Colors.black,
          fontSize: 32,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}

class PopularRoutes extends StatelessWidget {
  const PopularRoutes(this.globals);

  final Globals globals;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Positioned(
        left: 80,
        top: 612,
        child: Container(
          width: 1070,
          height: 388,
          child: DottedBorder(
            padding: const EdgeInsets.only(top: 50.0, left: 60),
            borderType: BorderType.RRect,
            color: AppColors.primary,
            strokeWidth: 3,
            dashPattern: [18, 18],
            radius: Radius.circular(32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Популярные направления',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: 48,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                SizedBox(
                  height: 30,
                ),
                Wrap(
                    spacing: 20,
                    runSpacing: 20,
                    children: globals.dataState.value == DataLoadingState.LOADING
                        ? [
                            ShimmerButton(width: 340, height: 78, baseColor: AppColors.secondary),
                            ShimmerButton(width: 400, height: 78, baseColor: AppColors.secondary),
                            ShimmerButton(width: 250, height: 78, baseColor: AppColors.secondary),
                            ShimmerButton(width: 360, height: 78, baseColor: AppColors.secondary),
                          ]
                        : globals.terminalPopularStations
                            .map((e) {
                              return AnimatedContainer(
                                height: 78,
                                duration: const Duration(seconds: 1),
                                curve: Curves.easeIn,
                                child: Material(
                                  color: AppColors.accent,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(24),
                                  ),
                                  child: InkWell(
                                    splashColor: AppColors.secondary,
                                    borderRadius: BorderRadius.circular(24),
                                    onTap: () {
                                      globals.selectedStationFrom.value.title = globals.currentStationConfig.value.defaultDispatch?.title ?? "";
                                      globals.selectedStationFrom.value.id = globals.currentStationConfig.value.defaultDispatch?.id ?? "";

                                      globals.selectedStationTo.value.title = e.title ?? "";
                                      globals.selectedStationTo.value.id = e.id ?? "";
                                      Get.toNamed('/selectDateScreen', arguments: {"station": e});
                                    },
                                    child: Container(
                                      height: 78,
                                      padding: const EdgeInsets.symmetric(horizontal: 36, vertical: 16),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            e.title ?? "na",
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: AppColors.primary,
                                              fontSize: 36,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            })
                            .take(4)
                            .toList()),
              ],
            ),
          ),
        ),
      );
    });
  }
}

class BuyTicket extends StatelessWidget {
  const BuyTicket(this.globals);

  final Globals globals;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 1190,
      top: 829,
      child: Obx(() {
        if (globals.dataState.value == DataLoadingState.LOADING) {
          return ShimmerButton(
            width: 650,
            height: 172,
            baseColor: AppColors.primary,
            highlightColor: AppColors.shimmerPrimaryHighlight,
            radius: 32,
          );
        } else {
          return AnimatedContainer(
            width: 650,
            duration: const Duration(seconds: 1),
            curve: Curves.easeIn,
            child: Material(
              color: Color(0xFF1E90FF),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(32),
              ),
              child: InkWell(
                borderRadius: BorderRadius.circular(32),
                splashColor: AppColors.primarySplashColor,
                onTap: () {
                  globals.selectedStationFrom.value.title = globals.currentStationConfig.value.defaultDispatch?.title ?? "na";
                  globals.selectedStationFrom.value.id = globals.currentStationConfig.value.defaultDispatch?.id ?? "na";
                  globals.selectedStationTo.value = Station();
                  Get.toNamed("/searchToScreen");
                },
                child: Container(
                  width: 650,
                  padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 40),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        'Купить билет',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 64,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      SizedBox(width: 67),
                      Image.asset('lib/assets/images/right.png', width: 24),
                    ],
                  ),
                ),
              ),
            ),
          );
        }
      }),
    );
  }
}

class MainHeader extends StatelessWidget {
  const MainHeader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 81,
      top: 230,
      child: Container(
        width: 1003,
        height: 224,
        child: Stack(
          children: [
            Positioned(
              left: 0,
              top: 0,
              child: Text(
                'Покупайте билеты',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 96,
                  fontWeight: FontWeight.w900,
                ),
              ),
            ),
            Positioned(
              left: 0,
              top: 102,
              child: Text(
                'быстрее чем в кассе',
                style: TextStyle(
                  color: Color(0xFF1E90FF),
                  fontSize: 96,
                  fontWeight: FontWeight.w900,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Shimer for buttons
class ShimmerButton extends StatelessWidget {
  ShimmerButton({
    super.key,
    required this.width,
    required this.height,
    required this.baseColor,
    this.highlightColor = AppColors.textSecondary,
    this.radius = 24,
  });
  final double width;
  final double height;
  final Color baseColor;
  Color highlightColor;
  double radius;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: baseColor,
      highlightColor: highlightColor,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: baseColor,
          borderRadius: BorderRadius.circular(radius),
        ),
      ),
    );
  }
}
