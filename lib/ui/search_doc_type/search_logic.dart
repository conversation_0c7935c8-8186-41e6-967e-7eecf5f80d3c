import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rxdart/rxdart.dart';
import 'package:terminal/src/globals.dart';
import '../../models/doc_type_response.dart';
import '../../src/api.dart';

class SearchDocumentTypeLogic extends GetxController {
  final api = Get.find<Api>();
  final globals = Get.find<Globals>();

  TextEditingController searchTextController = TextEditingController();
  FocusNode searchFocusNode = FocusNode();
  final searchText = "".obs;
  final docTypes = <DocType>[].obs;

  @override
  onInit() {
    docTypes.value = globals.docTypeList;

    searchTextController.addListener(() {
      searchText.value = searchTextController.text;
    });

    searchText.stream.debounceTime(Duration(milliseconds: 10)).listen((value) async {
      // final res = await api.getDispatchStationsByName(searchTextController.text) ;
      docTypes.value = globals.docTypeList.where((c) => c.title!.toLowerCase().startsWith(searchTextController.text.toLowerCase())).toList();
    });

    super.onInit();
  }

  List<Widget> generateItemsList(List<DocType> countries) {
    List<Widget> widgets = [];

    const cityStyle = TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.w800);

    Widget listItem(DocType doctype) {
      return InkWell(
        onTap: () {
          globals.tripOrder.selectedDocumentTypeId.value = doctype.id ?? 0;
          Get.back();
        },
        child: Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Container(
            width: 376,
            // height: 130,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.85),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "${doctype.title}",
                    style: cityStyle,
                    maxLines: null,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    void createCatalog(List<DocType> docTypes) {
      for (var docType in docTypes) {
        widgets.add(listItem(docType));
      }
    }

    if (docTypes.isEmpty) {
      return [];
    } else {
      createCatalog(countries);
    }

    return widgets;
  }
}
