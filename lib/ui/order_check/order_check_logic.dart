import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:terminal/models/order_to_place.dart';
import 'package:terminal/models/trip_order_model.dart';
import 'package:terminal/src/globals.dart';

class OrderCheckLogic extends GetxController {
  final globals = Get.find<Globals>();

  String baggageCount(int baggage) {
    if (baggage == 0) {
      return "нет багажа";
    } else {
      switch (baggage) {
        case 1:
          return "${baggage} место";
        case 2:
        case 3:
        case 4:
          return "${baggage} места";
        default:
          return "${baggage} мест";
      }
    }
  }

  @override
  void onInit() {
    super.onInit();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      globals.tripOrder.insuranceIncluded.value = true;
    });

    updateOrderTrip();

    super.onInit();
  }

  void updateOrderTrip() {
    globals.tripOrder.orderToPlace.value.trips = [];

    TripTrip _tripTrip = TripTrip(
        pointA: globals.tripOrder.selectedTrip.value.pointA?.id,
        start: globals.tripOrder.selectedTrip.value.start,
        tripId: globals.tripOrder.selectedTrip.value.tripId,
        pointB: globals.tripOrder.selectedTrip.value.pointB?.id,
        withoutPd: globals.tripOrder.selectedTrip.value.withoutPd);

    List<TicketOrder> _tickets = [];
    globals.tripOrder.passengersList.forEach((p) {
      _tickets.add(TicketOrder(
          firstName: p.firstName,
          lastName: p.lastName,
          secondName: p.secondName,
          dateOfBirthday: p.dateOfBirthday,
          typeDocument: p.typeDocument?.id,
          citizenship: p.citizenship?.id,
          seriaNumber: p.seriaNumber,
          isChild: p.isChild,
          sex: p.sex,
          countBaggage: p.baggage.value,
          insurance: globals.tripOrder.insuranceIncluded.value));
    });

    //Устанавливаем номер места для каждого пассажира
    _tickets.asMap().forEach((index, ticket) {
      ticket.placeNumber = globals.tripOrder.selectedSeats[index].seatNumber;

      /// Если не смогли определить взрослый/ребенок по дате рождения, то устанавливаем согласно выбранным местам
      if (ticket.isChild == null) {
        ticket.isChild = globals.tripOrder.selectedSeats[index].type == SeatType.CHILD;
      }
    });

    globals.tripOrder.orderToPlace.value.trips?.add(TripElement(trip: _tripTrip, tickets: _tickets));
  }
}
