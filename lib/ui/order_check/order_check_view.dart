import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/payment/payment/payment_logic.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import '../../models/qrstatus_response.dart';
import '../../src/globals.dart';
import '../helpers.dart';
import 'order_check_logic.dart';

class OrderCheckScreen extends StatelessWidget {
  final OrderCheckLogic logic = Get.put(OrderCheckLogic());
  final globals = Get.find<Globals>();

  OrderCheckScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isShowInsurance = globals.tripOrder.selectedTripDetail.value.insuranceInfo?.insuranceAvailable ?? false;
    return Scaffold(
      body: Container(
        width: 1920,
        height: 1080,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(color: Colors.white),
        child: Stack(
          children: [
            headerTitle(),
            BackOr<PERSON>ain<PERSON><PERSON>on(),
            passengersTitle(),
            baggageTitle(),
            footerBlock(),
            payButtons(),
            mainScreenButton(),
            seatsBlock(),
            Stack(
              children: [
                Positioned(
                  left: 80,
                  top: 299,
                  bottom: isShowInsurance ? 360 : 285,
                  child: SingleChildScrollView(
                    child: Column(
                      spacing: 20,
                      children: globals.tripOrder.passengersList.asMap().entries.map((entry) {
                        return passenger(entry.value, entry.key);
                      }).toList(),
                    ),
                  ),
                ),
                if (isShowInsurance) insurance(),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget passenger(Passenger passenger, int index) {
    return Row(
      spacing: 80,
      children: [
        Container(
          width: 600,
          height: 80,
          child: DottedBorder(
            padding: const EdgeInsets.symmetric(vertical: 26.0, horizontal: 36),
            borderType: BorderType.RRect,
            color: Color(0xFF1E90FF),
            strokeWidth: 2,
            dashPattern: [12, 12],
            radius: Radius.circular(20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              spacing: 12,
              children: [
                if (passenger.isEmpty)
                  Text(
                    "Пассажир ${index + 1}",
                    style: TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.w800),
                  ),
                if (!passenger.isEmpty)
                  Text(
                    passenger.fioString(),
                    style: TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.w800),
                  ),
                if (!passenger.isEmpty)
                  Text(
                    (passenger.docString()).toString(),
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 24,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                if (!passenger.isEmpty)
                  Text(
                    (passenger.seriaString()),
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 24,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),
        ),
        Obx(
          () {
            return Container(
              width: 450,
              height: 80,
              child: DottedBorder(
                padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16),
                borderType: BorderType.RRect,
                color: Color(0xFF1E90FF),
                strokeWidth: 2,
                dashPattern: [12, 12],
                radius: Radius.circular(20),
                child: Container(
                  child: (globals.tripOrder.selectedTripDetail.value.priceBaggage == null || globals.tripOrder.selectedTripDetail.value.priceBaggage == 0)
                      ? Center(
                          child: Text(
                          'Недоступен',
                          style: TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.w800),
                        ))
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 12.0),
                              child: Text(
                                logic.baggageCount(passenger.baggage.value),
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 24,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              spacing: 0,
                              children: [
                                InkWell(
                                    onTap: () {
                                      if (passenger.baggage.value > 0) {
                                        passenger.baggage.value--;
                                        globals.tripOrder.baggageCount.value = globals.tripOrder.selectedBaggageCount;
                                      } else {
                                        passenger.baggage.value = 0;
                                      }
                                    },
                                    child: Image.asset(
                                      "lib/assets/images/minus_b.png",
                                    )),
                                Container(
                                  color: Color(0xFF1E90FF),
                                  width: 1,
                                  height: double.infinity,
                                ),
                                InkWell(
                                  onTap: () {
                                    passenger.baggage.value++;
                                    globals.tripOrder.baggageCount.value = globals.tripOrder.selectedBaggageCount;
                                  },
                                  child: Image.asset(
                                    "lib/assets/images/plus_b.png",
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                ),
              ),
            );
          },
        )
      ],
    );
  }

  Widget orderHeader() {
    return Positioned(
      left: 127,
      top: 40,
      child: Text(
        'Ваш заказ',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Color(0xFF1E90FF),
          fontSize: 64,
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  Widget totalAmount() {
    return Obx(() {
      return Positioned(
        key: ValueKey(globals.tripOrder.selectedTripDetail.value.hashCode),
        left: 52,
        top: 761,
        right: 52,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'ИТОГО:',
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 36,
                fontWeight: FontWeight.w900,
              ),
            ),
            Text(
              globals.tripOrder.totalPrice.toString() + ' ₽',
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 36,
                fontWeight: FontWeight.w800,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget dividerLine() {
    return Positioned(
      left: 52,
      top: 157,
      right: 52,
      child: CustomPaint(
        size: Size(482, 3), // Размер линии
        painter: DottedLinePainter(),
      ),
    );
  }

  Widget timeAndDetails() {
    return Positioned(
      left: 52,
      top: 368,
      right: 0,
      child: Container(
        height: 91,
        child: Obx(
          () {
            return Stack(
              children: [
                Positioned(
                  left: 0,
                  top: 0,
                  child: Opacity(
                    opacity: 0.80,
                    child: Text(
                      'Время в пути: ${globals.tripOrder.selectedTripDetail.value.travelTimeHuman ?? ""}',
                      style: TextStyle(
                        color: Color(0xFF3E3B56),
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 0.29,
                  top: 33,
                  child: Opacity(
                    opacity: 0.80,
                    child: Text(
                      'Перевозчик: ${globals.tripOrder.selectedTripDetail.value.carrier ?? ""}',
                      style: TextStyle(
                        color: Color(0xFF3E3B56),
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 0,
                  top: 66,
                  child: Opacity(
                    opacity: 0.80,
                    child: Text(
                      'Автобус: ${globals.tripOrder.selectedTripDetail.value.busInfo ?? ""}',
                      style: TextStyle(
                        color: Color(0xFF3E3B56),
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget arrivalBlock() {
    return Positioned(
      right: 52,
      top: 193,
      child: Obx(
        () {
          return Container(
            width: 297,
            height: 132,
            child: Stack(
              children: [
                Positioned(
                  right: 0,
                  top: 37,
                  child: Text(
                    globals.tripOrder.endTimeString,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 48,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ),
                Positioned(
                  left: 177,
                  top: 0,
                  child: Text(
                    'Прибытие',
                    style: TextStyle(
                      color: Color(0xFF1E90FF),
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                Positioned(
                  right: 0,
                  top: 102,
                  child: Text(
                    "${globals.tripOrder.selectedTripDetail.value.pointB?.title ?? ""}",
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget departureBlock() {
    return Positioned(
      left: 52,
      top: 193,
      child: Obx(
        () {
          return Container(
            width: 258.29,
            height: 132,
            child: Stack(
              children: [
                Positioned(
                  left: 0,
                  top: 37,
                  child: Text(
                    globals.tripOrder.startTimeString,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 48,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ),
                Positioned(
                  left: 0,
                  top: 102,
                  child: Text(
                    globals.tripOrder.selectedTripDetail.value.pointA?.title ?? "",
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                Positioned(
                  left: 0,
                  top: 0,
                  child: Text(
                    'Отправление',
                    style: TextStyle(
                      color: Color(0xFF1E90FF),
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget seatsBlock() {
    return Positioned(
        top: 60,
        right: 60,
        child: Container(
          decoration: BoxDecoration(
            color: Color(0xFFF1F8FF),
            borderRadius: BorderRadius.circular(24),
          ),
          width: 590,
          height: 859,
          child: Stack(
            children: [
              orderHeader(),
              departureBlock(),
              arrivalBlock(),
              Obx(
                () {
                  return Positioned(
                    left: 52,
                    top: 490,
                    right: 52,
                    height: 245,
                    child: Column(
                      spacing: 8,
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (globals.tripOrder.adultsCount.value > 0)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                globals.tripOrder.adultsCount.value.toString() + ' взрослый',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                (globals.tripOrder.adultsCount.value * (globals.tripOrder.selectedTripDetail.value.priceFull ?? 0).toDouble()).toString() +
                                    ' ₽',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              )
                            ],
                          ),
                        if (globals.tripOrder.childrenCount.value > 0)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                globals.tripOrder.childrenCount.value.toString() + ' детский',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                (globals.tripOrder.childrenCount.value * (globals.tripOrder.selectedTripDetail.value.priceChild ?? 0).toDouble()).toString() +
                                    ' ₽',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              )
                            ],
                          ),
                        if (globals.tripOrder.baggageCount.value > 0)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                globals.tripOrder.baggageCount.value.toString() + ' багажный',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                (globals.tripOrder.baggageCount.value.toDouble() * (globals.tripOrder.selectedTripDetail.value.priceBaggage ?? 0).toDouble())
                                        .toString() +
                                    ' ₽',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              )
                            ],
                          ),
                        if (globals.tripOrder.noticeInfoMessage.value && (globals.tripOrder.selectedTripDetail.value.noticeInfo?.noticeAvailable ?? false))
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Оповещение об отмене',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                globals.tripOrder.noticeFee.toString() + ' ₽',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              )
                            ],
                          ),
                        if (globals.tripOrder.insuranceFee > 0)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Страхование',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                globals.tripOrder.insuranceFee.toString() + ' ₽',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              )
                            ],
                          ),
                        if (globals.tripOrder.serviceFee > 0)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Сервисный сбор автовокзала',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                globals.tripOrder.serviceFee.toString() + ' ₽',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              )
                            ],
                          ),

                        ///Комиссия за пассажирский билет
                        if (globals.tripOrder.commissionTicket > 0)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Комиссия за пассажирский билет',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                globals.tripOrder.commissionTicket.toString() + ' ₽',
                                style: TextStyle(
                                  color: Color(0xFF3E3B56),
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              )
                            ],
                          ),
                      ],
                    ),
                  );
                },
              ),
              totalAmount(),
              dividerLine(),
              timeAndDetails(),
            ],
          ),
        ));
  }

  Widget mainScreenButton() {
    return Positioned(
      left: 80,
      top: 941,
      child: MainBackButton(),
    );
  }

  Widget payButtons() {
    return Positioned(
      right: 60,
      top: 943,
      child: Container(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// Оплатить картой
            CustomInkWell(
              borderRadius: 12,
              color: Color(0xFFF1F8FF),
              onTap: () {
                logic.updateOrderTrip();
                Get.toNamed("/paymentScreen", arguments: {'paymentMethod': PaymentMethod.card.name});
              },
              child: Container(
                padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Оплатить картой',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 36,
                        fontFamily: 'Overpass',
                        fontWeight: FontWeight.w900,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            if (globals.tripOrder.selectedTripDetail.value.paymentButtons?.firstWhereOrNull((button) => button.slug == "sbp_qr") != null)
              Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(width: 24),
                  CustomInkWell(
                    borderRadius: 12,
                    color: AppColors.primary,
                    onTap: () {
                      logic.updateOrderTrip();
                      Get.toNamed("/paymentScreen", arguments: {'paymentMethod': PaymentMethod.spb.name});
                    },
                    child: Container(
                      padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Оплатить по СБП',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 36,
                              fontFamily: 'Overpass',
                              fontWeight: FontWeight.w900,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget insurance() {
    return Positioned(
      left: 80,
      top: 735.50,
      child: GestureDetector(
        onTap: () {
          globals.tripOrder.insuranceIncluded.toggle();
        },
        child: Container(
          height: 44,
          child: Row(
            children: [
              Obx(() {
                return Container(
                  width: 44,
                  height: 44,
                  decoration: ShapeDecoration(
                    color: Color(0xFFF1F8FF),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: BorderSide(width: 2, color: Color(0xFF1E90FF)),
                    ),
                  ),
                  child: globals.tripOrder.insuranceIncluded.value
                      ? Center(
                          child: Image.asset(
                            "lib/assets/images/check.png",
                            width: 20,
                            height: 20,
                          ),
                        )
                      : Container(),
                );
              }),
              SizedBox(width: 18),
              Text(
                'Страхование жизни и здоровья на время поездки',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 32,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget footerBlock() {
    return Positioned(
      left: 80,
      top: 819,
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: 'Нажимая кнопку',
              style: TextStyle(
                color: Colors.black,
                fontSize: 32,
                fontFamily: 'Overpass',
                fontWeight: FontWeight.w400,
              ),
            ),
            TextSpan(
              text: ' ',
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 32,
                fontFamily: 'Overpass',
                fontWeight: FontWeight.w400,
              ),
            ),
            TextSpan(
              text: '"Оплатить" вы соглашаетесь с условиями договора',
              style: TextStyle(
                color: Colors.black,
                fontSize: 32,
                fontFamily: 'Overpass',
                fontWeight: FontWeight.w400,
              ),
            ),
            TextSpan(
              text: ' \nПубличной оферты ',
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 32,
                fontFamily: 'Overpass',
                fontWeight: FontWeight.w400,
              ),
            ),
            TextSpan(
              text: 'и',
              style: TextStyle(
                color: Colors.black,
                fontSize: 32,
                fontFamily: 'Overpass',
                fontWeight: FontWeight.w400,
              ),
            ),
            TextSpan(
              text: ' Правилами страхования',
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 32,
                fontFamily: 'Overpass',
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Багаж
  Widget baggageTitle() {
    return Positioned(
      left: 760,
      top: 203,
      child: Text(
        'Багаж',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.black,
          fontSize: 36,
          fontFamily: 'Overpass',
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  /// Пассажиры
  Widget passengersTitle() {
    return Positioned(
      left: 79,
      top: 203,
      child: Text(
        'Пассажиры',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.black,
          fontSize: 36,
          fontFamily: 'Overpass',
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  /// Оплата заказа
  Widget headerTitle() {
    return Positioned(
      left: 369,
      top: 80,
      child: Text(
        'Оплата заказа',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.black,
          fontSize: 72,
          fontFamily: 'Overpass',
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }
}
