import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';
import 'login_logic.dart';

class LoginScreen extends StatelessWidget {
  final LoginLogic logic = Get.put(LoginLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey,
      body: Container(
        width: 1920,
        height: 1080,
        color: Colors.white,
        child: Stack(
          children: [
            Positioned(
                top: 80,
                left: 750,
                child: Text(
                  'Авторизация',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 64,
                    fontWeight: FontWeight.w800,
                  ),
                )),
            InputHost(logic),
            InputLogin(logic),
            InputPassword(logic),
            AuthButton(),
            Obx(() {
              return Positioned(bottom: 0, left: 0, right: 0, child: Keyboard(logic.currentTextController.value));
            })
          ],
        ),
      ),
    );
  }
}

class InputHost extends StatelessWidget {
  const InputHost(this.logic);

  final LoginLogic logic;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 250,
      left: 60,
      child: Container(
        width: 590,
        height: 150,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: TextField(
                cursorColor: Colors.black,
                cursorHeight: 64,
                cursorWidth: 2,
                cursorRadius: Radius.zero,
                onTapOutside: (event) {
                  FocusScope.of(context).requestFocus(logic.hostFocusNode);
                },
                showCursor: true,
                focusNode: logic.hostFocusNode,
                controller: logic.hostTextController,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 64,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Text(
              'Хост',
              style: TextStyle(
                color: Color(0xFFC5CCD4),
                fontSize: 36,
                fontWeight: FontWeight.w100,
              ),
            )
          ],
        ),
      ),
    );
  }
}

class InputLogin extends StatelessWidget {
  const InputLogin(this.logic);

  final LoginLogic logic;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 250,
      left: 665,
      child: Container(
        width: 590,
        height: 150,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: TextField(
                onTapOutside: (event) {
                  FocusScope.of(context).requestFocus(logic.loginFocusNode);
                },
                focusNode: logic.loginFocusNode,
                controller: logic.loginTextController,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 64,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Text(
              'Логин',
              style: TextStyle(
                color: Color(0xFFC5CCD4),
                fontSize: 36,
                fontWeight: FontWeight.w100,
              ),
            )
          ],
        ),
      ),
    );
  }
}

class InputPassword extends StatelessWidget {
  const InputPassword(this.logic);

  final LoginLogic logic;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 250,
      left: 1271,
      child: Container(
        width: 590,
        height: 150,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: TextField(
                onTapOutside: (event) {
                  FocusScope.of(context).requestFocus(logic.passwordFocusNode);
                },
                focusNode: logic.passwordFocusNode,
                obscureText: true,
                obscuringCharacter: '*',
                controller: logic.passwordTextController,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 64,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Text(
              'Пароль',
              style: TextStyle(
                color: Color(0xFFC5CCD4),
                fontSize: 36,
                fontWeight: FontWeight.w100,
              ),
            )
          ],
        ),
      ),
    );
  }
}

class AuthButton extends StatelessWidget {
  AuthButton({super.key});

  final logic = Get.put(LoginLogic());

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 552,
      left: 782,
      child: CustomInkWell(
        color: AppColors.primary,
        splashColor: AppColors.primarySplashColor,
        borderRadius: 12,
        onTap: () {
          logic.login();
        },
        child: Container(
          width: 355,
          height: 82,
          padding: const EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 16),
          child: Text(
            'Авторизоваться',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 36,
              fontWeight: FontWeight.w900,
            ),
          ),
        ),
      ),
    );
  }
}
