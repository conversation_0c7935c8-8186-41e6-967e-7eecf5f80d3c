import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:punycode_converter/punycode_converter.dart';
import '../../src/api.dart';
import '../../src/globals.dart';

class LoginLogic extends GetxController {
  final api = Get.find<Api>();
  final globals = Get.find<Globals>();

  TextEditingController hostTextController = TextEditingController();
  TextEditingController loginTextController = TextEditingController();
  TextEditingController passwordTextController = TextEditingController();

  FocusNode hostFocusNode = FocusNode();
  FocusNode loginFocusNode = FocusNode();
  FocusNode passwordFocusNode = FocusNode();

  final currentTextController = TextEditingController().obs;
  final currentFocusNode = FocusNode().obs;

  @override
  void onInit() {
    // TODO: implement onInit

    hostTextController.text = 'тест.авокзалы.рф';
    loginTextController.text = ' terminal_test';
    passwordTextController.text = 'Zg7A3eOceO';

    hostFocusNode.addListener(() {
      currentTextController.value = hostTextController;
      currentFocusNode.value = hostFocusNode;
    });

    loginFocusNode.addListener(() {
      currentTextController.value = loginTextController;
      currentFocusNode.value = loginFocusNode;
    });

    passwordFocusNode.addListener(() {
      currentTextController.value = passwordTextController;
      currentFocusNode.value = passwordFocusNode;
    });

    super.onInit();
  }

  login() async {
    final res = await api.login(hostTextController.text, loginTextController.text, passwordTextController.text);
    if (res.error == null) {
      globals.terminalData.value = res;
      globals.terminalData.update((data) {
        data?.host = Punycode.domainEncode(hostTextController.text);
      });
      Get.offAllNamed('/mainScreen');
    }
  }

  @override
  void onClose() {
    // TODO: implement onClose

    loginFocusNode.dispose();
    hostFocusNode.dispose();
    passwordFocusNode.dispose();

    hostTextController.dispose();
    loginTextController.dispose();
    passwordTextController.dispose();

    currentTextController.close();
    currentFocusNode.close();

    super.onClose();
  }
}
