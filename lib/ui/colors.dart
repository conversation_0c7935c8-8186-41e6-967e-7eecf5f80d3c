import 'package:flutter/material.dart';

class AppColors {
  static const primary = Color(0xFF1E90FF);
  static final secondary = Color(0x331E90FF); // 20% opacity of primary
  static final accent = Color(0xFF1E90FF).withValues(alpha: 0.1);
  static const textPrimary = Color(0xFF000000);
  static const background = Color(0xFFFFFFFF);
  static const textSecondary = Color(0xFFA1B2C6);
  static final shimmerBase = Color(0xFF1E90FF).withValues(alpha: 0.03);
  static final shimmerHighlight = Color(0xFF1E90FF).withValues(alpha: 0.1);
  static final shimmerPrimaryHighlight = Color(0x801E90FF);
  static const primarySplashColor = Color.fromARGB(255, 22, 104, 187);
  static final errorBackground = Color(0x33FF4C4C);
  static final enabledBorder = Color(0xFFC5CCD4);
  static final emptyStateListItem = Color(0xFFCDDEEF);
}
