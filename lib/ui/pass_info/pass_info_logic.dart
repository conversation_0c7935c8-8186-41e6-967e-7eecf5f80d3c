import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/models/qrstatus_response.dart';
import '../../src/api.dart';
import '../../src/globals.dart';

class PassInfoLogic extends GetxController {
  final globals = Get.find<Globals>();
  final api = Get.find<Api>();

  Timer? timer;
  String uuid = "";
  final link = ''.obs;
  int alreadyWaiting = 0;
  bool isAuthDialogOpen = false;
  final authSessionClosed = true.obs;
  QRAuthStatusResponse authUser = QRAuthStatusResponse.fromJson({});

  int checkPeriod = 5;
  int timeout = 20000;

  @override
  void onClose() {
    timer?.cancel();
    super.onClose();
  }

  @override
  void onInit() async {
    log("Выбрано ${globals.tripOrder.adultsCount.value} взрослых");
    log("Выбрано ${globals.tripOrder.childrenCount.value} детей");

    alreadyWaiting = 0;

    authSessionClosed.listen((val) {
      if (val) {
        Get.snackbar("Вы не успели войти", "Сессия закрыта, повторите вход",
            colorText: Colors.white, backgroundColor: Colors.blue, snackPosition: SnackPosition.TOP, duration: Duration(seconds: 5));
      }
    });

    super.onInit();
  }

  savePassengers() {
    globals.tripOrder.passengersList = globals.tripOrder.lkPassengersList.where((p) => p.isSelected.value).toList();
  }

  createQRCode() async {
    timer?.cancel();
    final res = await api.qrCodeCreate();
    uuid = res.uuid;
    link.value = "https://${globals.terminalData.value.host}${res.url}";
    log(link.value.toString());
    startWaitingAuth();
  }

  authDialogClosed() {
    timer?.cancel();
  }

  startWaitingAuth() async {
    timer?.cancel();
    alreadyWaiting = 0;
    authSessionClosed.value = false;

    timer = Timer.periodic(
      Duration(seconds: checkPeriod),
      (timer) async {
        alreadyWaiting += 5;
        authUser = await api.checkQRAuth(uuid);

        if (authUser.customer != null) {
          log("Наконец-то!! ${authUser.toJson()}");
          timer.cancel();
          globals.tripOrder.customer = authUser.customer;
          globals.tripOrder.lkPassengersList.value = authUser.customer?.passengers ?? [];
          Get.back();
          // uuid = res.uuid ;
        }

        if (alreadyWaiting > timeout) {
          timer.cancel();
          if (isAuthDialogOpen) {
            Get.back();
            authSessionClosed.value = true;
          }
        }
      },
    );
  }
}
