import 'dart:developer';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import '../../src/globals.dart';
import 'pass_info_logic.dart';
import 'package:terminal/models/qrstatus_response.dart';

class PassInfoScreen extends StatelessWidget {
  PassInfoScreen({Key? key}) : super(key: key);

  final PassInfoLogic logic = Get.put(PassInfoLogic());
  final globals = Get.find<Globals>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: 1920,
        height: 1080,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(color: Colors.white),
        child: Stack(
          children: [
            Positioned(
              left: 618,
              top: 80,
              child: Text(
                'Данные пассажиров',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 64,
                  fontWeight: FontWeight.w900,
                ),
              ),
            ),
            Positioned(
              left: 825,
              top: 941,
              child: CustomInkWell(
                color: Color(0xFFF1F8FF),
                splashColor: AppColors.textSecondary,
                borderRadius: 12,
                onTap: () {
                  // Get.back()
                  Get.toNamed('/passportScreen');
                  // Get.dialog(Dialog(
                  //   insetAnimationCurve: Curves.easeIn,
                  //   insetAnimationDuration: const Duration(milliseconds: 50),
                  //   insetPadding: EdgeInsets.all(8),
                  //   child: selectPassengersFromLK(),
                  // ));
                },
                child: Container(
                  padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Ввести вручную',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 36,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              left: 80,
              top: 941,
              child: MainBackButton(),
            ),
            Positioned(
              left: 991,
              top: 407,
              child: SizedBox(
                width: 836,
                child: Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: 'Для посадки на межобластные рейсы обязательно указать персональные данные.\n',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 32,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: ' \n',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: 'Вы можете использовать свой личный кабинет с сайта автовокзала, чтобы не вводить данные каждый раз при покупке билета.',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 32,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            Positioned(
              left: 162,
              top: 270,
              child: Container(
                width: 722,
                height: 515,
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(),
                child: Image.asset("lib/assets/images/pass.png"),
              ),
            ),
            Positioned(
              left: 1208,
              top: 941,
              child: CustomInkWell(
                color: AppColors.primary,
                splashColor: AppColors.primarySplashColor,
                borderRadius: 12,
                onTap: () async {
                  logic.createQRCode();
                  logic.isAuthDialogOpen = true;
                  await Get.dialog(Dialog(
                    insetAnimationCurve: Curves.easeIn,
                    insetAnimationDuration: const Duration(milliseconds: 50),
                    insetPadding: EdgeInsets.all(8),
                    child: lkAuthorizationDialog(),
                  ));

                  logic.isAuthDialogOpen = false;
                  logic.authDialogClosed();

                  log("PASSENGERS COUNT: ${logic.authUser.customer?.passengers?.length}");

                  if (logic.authUser.customer != null) {
                    if (logic.authUser.customer?.passengers?.length == 0) {
                      log("NO PASSENGERS");
                      Get.dialog(Dialog(
                        insetAnimationCurve: Curves.easeIn,
                        insetAnimationDuration: const Duration(milliseconds: 50),
                        insetPadding: EdgeInsets.all(8),
                        child: noSavedPassengers(),
                      ));
                    } else {
                      log("PASSENGERS: ${logic.authUser.customer?.passengers?.map((e) => e.toJson()).toList()}");
                      Get.dialog(Dialog(
                        insetAnimationCurve: Curves.easeIn,
                        insetAnimationDuration: const Duration(milliseconds: 50),
                        insetPadding: EdgeInsets.all(8),
                        child: selectPassengersFromLK(),
                      ));
                    }
                  }
                },
                child: Container(
                  padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Использовать личный кабинет',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 36,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            BackOrMainButton(),
          ],
        ),
      ),
    );
  }

  //Экран без сохраненных пассажиров в ЛК
  Widget noSavedPassengers() {
    return Container(
      width: 900,
      height: 986,
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32),
        ),
      ),
      child: Stack(
        children: [
          //Выбор пассажира
          Positioned(
            left: 230,
            top: 80,
            child: Text(
              'Выбор пассажира',
              style: TextStyle(
                color: Colors.black,
                fontSize: 48,
                fontFamily: 'Overpass',
                fontWeight: FontWeight.w900,
                height: 0,
              ),
            ),
          ),

          //К сожалению, в вашем личном кабинете пока нет сохраненных пассажиров
          Positioned(
            left: 60,
            top: 614,
            child: SizedBox(
              width: 780,
              child: Text(
                'К сожалению, в вашем личном кабинете пока нет сохраненных пассажиров.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 32,
                  fontFamily: 'Overpass',
                  fontWeight: FontWeight.w500,
                  height: 0,
                ),
              ),
            ),
          ),

          //В этот раз придется ввести их вручную, но зато мы сохраним их для ваших будущих поездок.
          Positioned(
            left: 60,
            top: 716,
            child: SizedBox(
              width: 780,
              child: Text(
                'В этот раз придется ввести их вручную, но зато мы сохраним их для ваших будущих поездок.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 32,
                  fontFamily: 'Overpass',
                  fontWeight: FontWeight.w500,
                  height: 0,
                ),
              ),
            ),
          ),

          //Ввести пассажиров вручную
          Positioned(
            left: 60,
            top: 844,
            child: CustomInkWell(
              color: AppColors.primary,
              splashColor: AppColors.primarySplashColor,
              borderRadius: 12,
              onTap: () {
                Get.back();
                Get.toNamed('/passportScreen');
              },
              child: Container(
                width: 780,
                padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'Ввести пассажиров вручную',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 36,
                        fontFamily: 'Overpass',
                        fontWeight: FontWeight.w900,
                        height: 0,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          Positioned(
            left: 257,
            top: 181,
            child: Container(
              width: 385,
              height: 384,
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(),
              child: Image.asset("lib/assets/images/select_pass.png"),
            ),
          ),
        ],
      ),
    );
  }

  //Экран с выбором пассажиров из ЛК
  Widget selectPassengersFromLK() {
    return Obx(
      () {
        return Container(
          width: 900,
          height: 1055,
          clipBehavior: Clip.antiAlias,
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
          ),
          padding: EdgeInsets.all(60),
          child: Column(
            spacing: 30,
            children: [
              Text(
                'Выбор пассажиров',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 48,
                  fontFamily: 'Overpass',
                  fontWeight: FontWeight.w900,
                  height: 0,
                ),
              ),
              Obx(() {
                final adultsCount =
                    globals.tripOrder.adultsCount.value - globals.tripOrder.lkPassengersList.where((p) => (!(p.isChild ?? false) && p.isSelected.value)).length;
                final childrenCount = globals.tripOrder.childrenCount.value - globals.tripOrder.lkSelectedChildrenCount;

                return Text(
                  "Взрослых ${globals.tripOrder.adultsCount.value - adultsCount}/${globals.tripOrder.adultsCount.value}  |  Детей  ${globals.tripOrder.childrenCount.value - childrenCount}/${globals.tripOrder.childrenCount.value}",
                  style: TextStyle(color: Colors.black, fontSize: 24, fontFamily: 'Overpass', fontWeight: FontWeight.w700, height: 0),
                );
              }),
              Text(
                'В вашем личном кабинете есть данные этих пассажиров. Отметьте кто из них поедет.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 32,
                  fontFamily: 'Overpass',
                  fontWeight: FontWeight.w500,
                  height: 0,
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(top: 2),
                  child: Container(
                    child: Column(
                      spacing: 20,
                      children: globals.tripOrder.lkPassengersList.map((e) {
                        final pass = Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 8,
                          children: [
                            Text(
                              (e.lastName ?? "") + ' ' + (e.firstName ?? "") + ' ' + (e.secondName ?? ""),
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 24,
                                fontFamily: 'Overpass',
                                fontWeight: FontWeight.w800,
                                height: 0,
                              ),
                            ),
                            Text(
                              "${e.typeDocument?.title} ${e.seriaNumber}",
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 24,
                                fontFamily: 'Overpass',
                                fontWeight: FontWeight.w500,
                                height: 0,
                              ),
                            ),
                          ],
                        );

                        return InkWell(
                          onTap: () {
                            if (!e.isSelected.value) {
                              if ((e.isChild ?? false) && globals.tripOrder.lkSelectedChildrenCount < globals.tripOrder.childrenCount.value) {
                                e.isSelected.value = true;
                              }

                              if (!(e.isChild ?? false) && globals.tripOrder.lkSelectedAdultsCount < globals.tripOrder.adultsCount.value) {
                                e.isSelected.value = true;
                              }
                            } else {
                              e.isSelected.value = false;
                            }
                          },
                          child: Container(
                            child: Opacity(
                              opacity: getPassengersOpacity(e),
                              child: Row(
                                spacing: 20,
                                children: [
                                  !e.isSelected.value
                                      ? Container(
                                          width: 640,
                                          height: 120,
                                          child: DottedBorder(
                                              borderType: BorderType.RRect,
                                              color: Color(0xFF1E90FF),
                                              strokeWidth: 3,
                                              dashPattern: [8, 8],
                                              radius: Radius.circular(20),
                                              padding: EdgeInsets.symmetric(vertical: 26, horizontal: 38),
                                              child: pass),
                                        )
                                      : Container(
                                          width: 640,
                                          height: 120,
                                          decoration: ShapeDecoration(
                                            shape: RoundedRectangleBorder(
                                              side: BorderSide(width: 2, color: Color(0xFF1E90FF)),
                                              borderRadius: BorderRadius.circular(20),
                                            ),
                                          ),
                                          padding: EdgeInsets.symmetric(vertical: 24, horizontal: 36),
                                          child: pass,
                                        ),
                                  Container(
                                    width: 120,
                                    height: 120,
                                    decoration: ShapeDecoration(
                                      color: Color(0x331E90FF),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                    ),
                                    child: e.isSelected.value ? Image.asset("lib/assets/images/check.png") : Container(),
                                  )
                                ],
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),

              //Ввести остальных вручную
              Opacity(
                opacity: (globals.tripOrder.lkPassengersList.where((p) => p.isSelected.value).length < globals.tripOrder.totalSelectedSeats) ? 1 : 0.3,
                child: CustomInkWell(
                  borderRadius: 12,
                  color: Color(0xFFF1F8FF),
                  onTap: () {
                    if (globals.tripOrder.lkPassengersList.where((p) => p.isSelected.value).length < globals.tripOrder.totalSelectedSeats) {
                      logic.savePassengers();
                      Get.back();
                      Get.toNamed('/passportScreen');
                    } else {
                      WarningHandler.showWarning("Вводить пассажиров вручную не нужно!", "Вы уже выбрали достаточное количество пассажиров.");
                    }
                  },
                  child: Container(
                    width: 780,
                    padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          'Ввести остальных вручную',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 36,
                            fontFamily: 'Overpass',
                            fontWeight: FontWeight.w900,
                            height: 0,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              //Продолжить
              Opacity(
                opacity: (globals.tripOrder.lkPassengersList.where((p) => p.isSelected.value).length >= globals.tripOrder.totalSelectedSeats) ? 1 : 0.3,
                child: CustomInkWell(
                  borderRadius: 12,
                  color: AppColors.primary,
                  onTap: () {
                    if (globals.tripOrder.lkPassengersList.where((p) => p.isSelected.value).length == globals.tripOrder.totalSelectedSeats) {
                      logic.savePassengers();
                      Get.back();
                      if ((globals.tripOrder.selectedTripDetail.value.noticeInfo?.noticeAvailable ?? false)) {
                        //globals.tripOrder.noticeInfoMessage.value = true;
                        globals.tripOrder.noticeInfoMessage.value = (globals.tripOrder.selectedTripDetail.value.noticeInfo?.useNoticeDefault ?? true);
                        Get.toNamed('/passContactsScreen');
                      } else {
                        Get.toNamed('/orderCheckScreen');
                      }
                    } else {
                      WarningHandler.showWarning("Внимание", "Вы не выбрали достаточное количество пассажиров.");
                    }
                  },
                  child: Container(
                    width: 780,
                    padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          'Продолжить',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 36,
                            fontFamily: 'Overpass',
                            fontWeight: FontWeight.w900,
                            height: 0,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget lkAuthorizationDialog() {
    return Container(
      width: 900,
      height: 978,
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32),
        ),
      ),
      child: Stack(
        children: [
          Positioned(
            left: 226,
            top: 80,
            child: Text(
              'Авторизация в ЛК',
              style: TextStyle(
                color: Colors.black,
                fontSize: 48,
                fontWeight: FontWeight.w900,
              ),
            ),
          ),
          Positioned(
            left: 60,
            top: 177,
            child: SizedBox(
              width: 780,
              child: Text(
                'Для входа в личный кабинет отсканируйте\nQR-код камерой вашего телефона.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 32,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          Positioned(
            left: 253,
            top: 295,
            child: Container(
              width: 394,
              height: 370,
              child: Obx(
                () {
                  if (logic.link.value == "") {
                    return Center(child: CircularProgressIndicator());
                  } else
                    return PrettyQrView.data(
                      data: logic.link.value.toString(),
                      errorCorrectLevel: QrErrorCorrectLevel.M,
                      decoration: const PrettyQrDecoration(
                        shape: PrettyQrSmoothSymbol(
                          color: Colors.black,
                          roundFactor: 0, // Убираем закругления
                        ),
                        image: PrettyQrDecorationImage(
                          image: AssetImage('lib/assets/images/av.png'),
                        ),
                      ),
                    );
                },
              ),
            ),
          ),
          Positioned(
            left: 60,
            top: 721,
            child: Container(
              width: 780,
              height: 197,
              child: Stack(
                children: [
                  Positioned(
                    left: 0,
                    top: 0,
                    child: Container(
                      width: 780,
                      height: 197,
                      decoration: ShapeDecoration(
                        color: Color(0x331E90FF),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    left: 49,
                    top: 38,
                    child: Container(
                      width: 647,
                      height: 120,
                      child: Stack(
                        children: [
                          Positioned(
                            left: 0,
                            top: 0,
                            child: Container(
                              width: 120,
                              height: 120,
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Image.asset("lib/assets/images/security.png"),
                                ],
                              ),
                            ),
                          ),
                          Positioned(
                            left: 177,
                            top: 19,
                            child: Container(
                              width: 480,
                              height: 82,
                              child: Stack(
                                children: [
                                  Positioned(
                                    left: 0,
                                    top: 0,
                                    child: Text(
                                      'Билеты всегда в сохранности',
                                      style: TextStyle(
                                        color: Color(0xFF1E90FF),
                                        fontSize: 32,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    left: 0,
                                    top: 41,
                                    child: Text(
                                      'в вашем личном кабинете',
                                      style: TextStyle(
                                        color: Color(0xFF1E90FF),
                                        fontSize: 32,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  double getPassengersOpacity(Passenger e) {
    if (e.isSelected.value) {
      return 1;
    }

    if ((e.isChild ?? false) && globals.tripOrder.lkSelectedChildrenCount < globals.tripOrder.childrenCount.value) {
      return 1;
    }

    if (!(e.isChild ?? false) && globals.tripOrder.lkSelectedAdultsCount < globals.tripOrder.adultsCount.value) {
      return 1;
    }
    return 0.3;
  }
}
