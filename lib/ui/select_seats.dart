import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/models/trip_detail_model.dart';
import 'package:terminal/models/trip_order_model.dart';

import '../src/globals.dart';

class SeatWidget extends StatelessWidget {
  final Map<String, dynamic> seatsSchema;
  final List<int> freePlaces;

  // final RxList<int> selectedSeats;

  SeatWidget({required this.seatsSchema, required this.freePlaces, required this.globals});

  static const double seatHeight = 72;
  static const double seatWidth = 72;

  final Globals globals;

  @override
  Widget build(BuildContext context) {
    // log("maxRows: $maxRows maxCols: $maxCols");
    return Obx(() {
      List<Widget> positionedWidgets = [];

      int maxRows = seatsSchema.keys.map(int.parse).reduce((a, b) => a > b ? a : b);
      int maxCols = seatsSchema.values.expand((row) => row.keys.map(int.parse)).reduce((a, b) => a > b ? a : b);

      // log("updated") ;
      seatsSchema.forEach((rowKey, rowValue) {
        int row = int.parse(rowKey);
        rowValue.forEach((colKey, colValue) {
          int col = int.parse(colKey);
          SeatsSchema seat = colValue;

          // log("seat: ${seat.number} row: $row col: $col");

          if (seat.number != null) {
            positionedWidgets.add(
              Positioned(
                left: maxRows * seatWidth - row * seatWidth,
                top: maxCols * seatHeight - col * seatHeight,
                child: InkWell(
                  onTap: () async {
                    var result = SeatType.ADULT;
                    if (seat.status != Status.BUSY) {
                      if (globals.tripOrder.selectedSeats.indexWhere((s) => s.seatNumber == seat.number) != -1) {
                        globals.tripOrder.selectedSeats.removeWhere((s) => s.seatNumber == seat.number);
                      } else {
                        if ((globals.tripOrder.selectedTripDetail.value.priceChild ?? 0) > 0) {
                          result = await Get.dialog(Dialog(
                              insetAnimationCurve: Curves.easeIn,
                              insetAnimationDuration: const Duration(milliseconds: 50),
                              insetPadding: EdgeInsets.all(8),
                              child: selectAgeDialog(seat.number!)));
                        }

                        globals.tripOrder.selectedSeats.add(Seat(
                          seatNumber: seat.number!,
                          type: result,
                        ));
                      }
                    }
                  },
                  child: Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: _getBgColor(seat.status, seat.number, freePlaces, globals.tripOrder.selectedSeats),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: _getBorderColor(seat.status, seat.number, freePlaces, globals.tripOrder.selectedSeats),
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        seat.number.toString(),
                        // textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _getColor(seat.status, seat.number, freePlaces, globals.tripOrder.selectedSeats),
                          fontSize: 32,
                          fontWeight: FontWeight.w800,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }
        });
      });
      return Container(
        // key: ValueKey(globals.selectedSeats.value.toString()),
        width: maxRows * seatHeight,
        height: maxCols * seatWidth,
        child: Stack(
          children: positionedWidgets,
        ),
      );
    });
  }

  Color _getBorderColor(Status? status, int? number, List<int> freePlaces, List<Seat> selectedPlaces) {
    if (status == Status.EMPTY) {
      return Colors.transparent;
    } else if (status == Status.BUSY) {
      return Colors.grey;
    } else if (selectedPlaces.firstWhereOrNull((p) => p.seatNumber == number) != null) {
      return Colors.blue;
    } else {
      return Colors.transparent;
    }
  }

  Color _getColor(Status? status, int? number, List<int> freePlaces, List<Seat> selectedPlaces) {
    if (status == Status.EMPTY) {
      return Color(0x33000000);
    } else if (status == Status.BUSY) {
      return Color(0x33000000);
    } else if (selectedPlaces.firstWhereOrNull((p) => p.seatNumber == number) != null) {
      return Colors.white;
    } else if (freePlaces.contains(number)) {
      return Colors.black;
    } else {
      return Colors.black;
    }
  }

  Color _getBgColor(Status? status, int? number, List<int> freePlaces, List<Seat> selectedPlaces) {
    if (status == Status.EMPTY) {
      return Colors.transparent;
    } else if (status == Status.BUSY) {
      return Color(0xFFCFDBE7);
    } else if (selectedPlaces.firstWhereOrNull((p) => p.seatNumber == number) != null) {
      final seat = selectedPlaces.firstWhereOrNull((p) => p.seatNumber == number);
      if (seat?.type == SeatType.ADULT) {
        return Colors.blue;
      } else {
        return Colors.green;
      }
    } else if (freePlaces.contains(number)) {
      return Color(0xFFF1F8FF);
    } else {
      return Colors.white;
    }
  }

  Widget selectAgeDialog(int seatNumber) {
    return Container(
      width: 900,
      height: 300,
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32),
        ),
      ),
      child: Stack(
        children: [
          Positioned(
            left: 60,
            top: 60,
            child: InkWell(
              onTap: () {
                Get.back(result: SeatType.ADULT);
              },
              child: Container(
                width: 380,
                height: 180,
                decoration: ShapeDecoration(
                  color: Color(0x331E90FF),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: Center(
                  child: Text(
                    'Взрослый',
                    style: TextStyle(
                      color: Color(0xFF1E90FF),
                      fontSize: 36,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            left: 460,
            top: 60,
            child: InkWell(
              onTap: () {
                log('ДЕТСКИЙ');
                Get.back(result: SeatType.CHILD);
              },
              child: Container(
                width: 380,
                height: 180,
                decoration: ShapeDecoration(
                  color: Colors.green[100],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: Center(
                  child: Text(
                    'Детский',
                    style: TextStyle(
                      color: Color(0xFF1E90FF),
                      fontSize: 36,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
