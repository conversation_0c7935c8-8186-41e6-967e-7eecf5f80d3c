import 'dart:ui';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/models/create_order_model.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';
import 'package:terminal/ui/payment/return_ticket_choose/return_ticket_choose_logic.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';

class ReturnTicketChooseScreen extends StatelessWidget {
  final logic = Get.put(ReturnTicketChooseLogic());
  final globals = Get.find<Globals>();

  ReturnTicketChooseScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: 1920,
        height: 1080,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(color: Colors.white),
        child: Stack(
          children: [
            seatsBlock(),
            title(),
            infoText(),
            returnButtons(),
            mainScreenButton(),
            BackOrMainButton(),
            logic.isPasssengeNotEmpty()
                ? PassengerListWidget()
                : Center(
                    child: Text(
                      "Нет доступных билетов к возврату",
                      style: TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.w800),
                    ),
                  )
          ],
        ),
      ),
    );
  }

  Widget returnButtons() {
    return Positioned(
      right: 60,
      top: 943,
      child: Container(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(width: 24),
                Obx(() {
                  final bool isEnabled = logic.isPasssengeNotEmpty() && logic.isAnyTicketSelected();
                  return CustomInkWell(
                    borderRadius: 12,
                    color: isEnabled ? AppColors.primary : Color(0xFFF1F8FF),
                    onTap: () {
                      if (isEnabled) {
                        logic.returnChooseTickets();
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Провести возврат',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: isEnabled ? Colors.white : Colors.black12,
                              fontSize: 36,
                              fontFamily: 'Overpass',
                              fontWeight: FontWeight.w900,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Текст под заголовком
  Widget infoText() {
    return Positioned(
      left: 80,
      top: 200,
      width: 1055,
      child: Text(
        "Отметьте галочками билеты, которые вы хотите вернуть.",
        textAlign: TextAlign.left,
        style: TextStyle(
          color: Colors.black,
          fontSize: 32,
          fontFamily: 'Overpass',
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  /// Кнопка возврат на главный экран
  Widget mainScreenButton() {
    return Positioned(
      left: 80,
      top: 941,
      child: MainBackButton(),
    );
  }

  /// Билеты к возврату
  Widget title() {
    return Positioned(
      left: 286,
      top: 80,
      child: Text(
        'Билеты к возврату',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.black,
          fontSize: 72,
          fontFamily: 'Overpass',
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  Widget orderHeader() {
    return Positioned(
      left: 127,
      top: 40,
      child: Text(
        'Ваш заказ',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Color(0xFF1E90FF),
          fontSize: 64,
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  Widget totalAmount() {
    return Obx(() {
      return Positioned(
        key: ValueKey(globals.tripOrder.selectedTripDetail.value.hashCode),
        left: 52,
        top: 761,
        right: 52,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'ИТОГО:',
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 36,
                fontWeight: FontWeight.w900,
              ),
            ),
            Text(
              //TOOD: - Добавить отображение стоимости
              "",
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 36,
                fontWeight: FontWeight.w800,
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget dividerLine() {
    return Positioned(
      left: 52,
      top: 157,
      right: 52,
      child: CustomPaint(
        size: Size(482, 3),
        painter: DottedLinePainter(),
      ),
    );
  }

  Widget timeAndDetails() {
    return Positioned(
      left: 52,
      top: 368,
      right: 0,
      child: Container(
        height: 91,
        child: Obx(() {
          final returnTicket = globals.returnTickets.first;
          return Stack(
            children: [
              Positioned(
                left: 0,
                top: 0,
                child: Opacity(
                  opacity: 0.80,
                  child: Text(
                    'Время в пути: ${returnTicket.trip?.travelTimeHuman ?? ""}',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              Positioned(
                left: 0.29,
                top: 33,
                child: Opacity(
                  opacity: 0.80,
                  child: Text(
                    'Перевозчик: ${returnTicket.trip?.carrier ?? ""}',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              if (returnTicket.trip?.busInfo != null)
                Positioned(
                  left: 0,
                  top: 66,
                  child: Opacity(
                    opacity: 0.80,
                    child: Text(
                      'Автобус: ${returnTicket.trip?.busInfo}',
                      style: TextStyle(
                        color: Color(0xFF3E3B56),
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          );
        }),
      ),
    );
  }

  Widget arrivalBlock() {
    return Positioned(
      right: 52,
      top: 193,
      child: Obx(() {
        final returnTickets = globals.returnTickets.first;
        return Container(
          width: 297,
          height: 132,
          child: Stack(
            children: [
              Positioned(
                right: 0,
                top: 37,
                child: Text(
                  returnTickets.trip?.endTimeString ?? "",
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 48,
                    fontWeight: FontWeight.w800,
                  ),
                ),
              ),
              Positioned(
                left: 177,
                top: 0,
                child: Text(
                  'Прибытие',
                  style: TextStyle(
                    color: Color(0xFF1E90FF),
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              Positioned(
                right: 0,
                top: 102,
                child: Text(
                  returnTickets.trip?.pointB?.title ?? "",
                  style: TextStyle(
                    color: Color(0xFF3E3B56),
                    fontSize: 24,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget departureBlock() {
    return Positioned(
      left: 52,
      top: 193,
      child: Obx(() {
        final returnTickets = globals.returnTickets.first;
        return Container(
          width: 258.29,
          height: 132,
          child: Stack(
            children: [
              Positioned(
                left: 0,
                top: 37,
                child: Text(
                  returnTickets.trip?.startTimeString ?? "",
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 48,
                    fontWeight: FontWeight.w800,
                  ),
                ),
              ),
              Positioned(
                left: 0,
                top: 102,
                child: Text(
                  returnTickets.trip?.pointA?.title ?? "",
                  style: TextStyle(
                    color: Color(0xFF3E3B56),
                    fontSize: 24,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              Positioned(
                left: 0,
                top: 0,
                child: Text(
                  'Отправление',
                  style: TextStyle(
                    color: Color(0xFF1E90FF),
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget seatsBlock() {
    return Positioned(
      top: 60,
      right: 60,
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFF1F8FF),
          borderRadius: BorderRadius.circular(24),
        ),
        width: 590,
        height: 859,
        child: Stack(
          children: [
            orderHeader(),
            departureBlock(),
            arrivalBlock(),
            Obx(() {
              return Positioned(
                left: 52,
                top: 490,
                right: 52,
                height: 245,
                child: Column(
                  spacing: 8,
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (globals.tripOrder.adultsCount.value > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            globals.tripOrder.adultsCount.value.toString() + ' взрослый',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            (globals.tripOrder.adultsCount.value * (globals.tripOrder.selectedTripDetail.value.priceFull ?? 0).toDouble()).toString() + ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    if (globals.tripOrder.childrenCount.value > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            globals.tripOrder.childrenCount.value.toString() + ' детский',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            (globals.tripOrder.childrenCount.value * (globals.tripOrder.selectedTripDetail.value.priceChild ?? 0).toDouble()).toString() + ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    if (globals.tripOrder.baggageCount.value > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            globals.tripOrder.baggageCount.value.toString() + ' багажный',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            (globals.tripOrder.baggageCount.value.toDouble() * (globals.tripOrder.selectedTripDetail.value.priceBaggage ?? 0).toDouble())
                                    .toString() +
                                ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    if (globals.tripOrder.noticeInfoMessage.value && (globals.tripOrder.selectedTripDetail.value.noticeInfo?.noticeAvailable ?? false))
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Оповещение об отмене',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            globals.tripOrder.noticeFee.toString() + ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    if (globals.tripOrder.insuranceFee > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Страхование',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            globals.tripOrder.insuranceFee.toString() + ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    if (globals.tripOrder.serviceFee > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Сервисный сбор автовокзала',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            globals.tripOrder.serviceFee.toString() + ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),

                    ///Комиссия за пассажирский билет
                    if (globals.tripOrder.commissionTicket > 0)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Комиссия за пассажирский билет',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            globals.tripOrder.commissionTicket.toString() + ' ₽',
                            style: TextStyle(
                              color: Color(0xFF3E3B56),
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                  ],
                ),
              );
            }),
            //TODO: - Добавить отображение стоимости
            //totalAmount(),
            dividerLine(),
            timeAndDetails(),
          ],
        ),
      ),
    );
  }
}

class PassengerListWidget extends StatefulWidget {
  @override
  _PassengerListWidgetState createState() => _PassengerListWidgetState();
}

class _PassengerListWidgetState extends State<PassengerListWidget> {
  final ScrollController _scrollController = ScrollController();
  final globals = Get.find<Globals>();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 80, top: 326, bottom: 200),
      child: SizedBox(
        height: MediaQuery.of(context).size.height,
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(
            scrollbars: true,
            dragDevices: {
              PointerDeviceKind.touch,
              PointerDeviceKind.mouse,
            },
          ),
          child: Scrollbar(
            controller: _scrollController,
            thumbVisibility: true,
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  subTitle(),
                  SizedBox(height: 24),
                  Column(
                    children: globals.returnTickets.asMap().entries.map((entry) {
                      int index = entry.key;
                      Ticket ticket = entry.value;
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 20),
                        child: passenger(ticket, ticket.isReturned, index + 1),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Список билетов
  Widget subTitle() {
    return Text(
      'Список билетов',
      textAlign: TextAlign.center,
      style: TextStyle(
        color: Colors.black,
        fontSize: 36,
        fontFamily: 'Overpass',
        fontWeight: FontWeight.w800,
      ),
    );
  }

  Widget passenger(Ticket ticket, bool isReturned, int index) {
    const double containerWidth = 1033;
    const double containerHeight = 77;

    return Obx(() {
      final selected = ticket.isSelected.value;

      Widget mainContent;

      if (isReturned) {
        mainContent = Container(
          width: containerWidth,
          height: containerHeight,
          child: DottedBorder(
            padding: const EdgeInsets.symmetric(vertical: 26.0, horizontal: 36),
            borderType: BorderType.RRect,
            color: const Color(0xFF1A1B2C6),
            strokeWidth: 2,
            dashPattern: [12, 12],
            radius: const Radius.circular(20),
            child: rowPassenger(ticket, isReturned),
          ),
        );
      } else {
        mainContent = selected
            ? Container(
                width: containerWidth,
                height: containerHeight,
                padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 36),
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    side: const BorderSide(width: 2, color: Color(0xFF1E90FF)),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: rowPassenger(ticket, isReturned),
              )
            : Container(
                width: containerWidth,
                height: containerHeight,
                child: DottedBorder(
                  padding: const EdgeInsets.symmetric(vertical: 26.0, horizontal: 36),
                  borderType: BorderType.RRect,
                  color: const Color(0xFF1E90FF),
                  strokeWidth: 2,
                  dashPattern: [12, 12],
                  radius: const Radius.circular(20),
                  child: rowPassenger(ticket, isReturned),
                ),
              );
      }

      return Row(
        children: [
          mainContent,
          if (!isReturned) SizedBox(width: 20),
          if (!isReturned)
            InkWell(
              onTap: () {
                ticket.isSelected.toggle();
              },
              child: Container(
                width: 77,
                height: 77,
                decoration: ShapeDecoration(
                  color: AppColors.secondary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: selected
                    ? Center(
                        child: Image.asset(
                          "lib/assets/images/check_3.png",
                          width: 48,
                          height: 41,
                        ),
                      )
                    : null,
              ),
            ),
        ],
      );
    });
  }

  /// Cтрока с пустым пассажиром
  // Widget rowNoNamePassenger(int index, bool isReturned) {
  //   return Row(
  //     crossAxisAlignment: CrossAxisAlignment.center,
  //     mainAxisAlignment: MainAxisAlignment.start,
  //     spacing: 12,
  //     children: [
  //       Text(
  //         "Пассажир ${index}",
  //         style: TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.w800),
  //       ),
  //       if (isReturned)
  //         Text(
  //           ("Возвращен"),
  //           style: TextStyle(
  //             color: AppColors.primary,
  //             fontSize: 24,
  //             fontWeight: FontWeight.w800,
  //           ),
  //         ),
  //     ],
  //   );
  // }

  /// Строка с пассажиром
  Widget rowPassenger(Ticket ticket, bool isReturned) {
    final fio = ticket.passenger?.fioString();
    final doc = ticket.passenger?.docString();
    final seria = ticket.passenger?.seriaString();
    final ticketNumber = ticket.ticketNumber;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      spacing: 12,
      children: [
        if (ticketNumber != null)
          Text(
            ticketNumber,
            style: TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.w800),
          ),
        if (fio != null)
          Text(
            fio,
            style: TextStyle(fontSize: 24, color: Colors.black, fontWeight: FontWeight.w800),
          ),
        if (doc != null)
          Text(
            doc,
            style: TextStyle(color: Colors.black, fontSize: 24, fontWeight: FontWeight.w500),
          ),
        if (seria != null)
          Text(
            seria,
            style: TextStyle(color: Colors.black, fontSize: 24, fontWeight: FontWeight.w500),
          ),
        if (isReturned)
          Text(
            ("Возвращен"),
            style: TextStyle(color: AppColors.primary, fontSize: 24, fontWeight: FontWeight.w800),
          ),
      ],
    );
  }
}
