import 'package:get/get.dart';
import 'package:terminal/src/api.dart';
import 'package:terminal/src/globals.dart';

class ReturnTicketChooseLogic extends GetxController {
  final globals = Get.find<Globals>();
  final api = Get.find<Api>();

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {}

  Future<void> returnChooseTickets() async {
    Get.toNamed('/returnTicketProcessScreen');
  }

  bool isPasssengeNotEmpty() {
    return globals.returnTickets.isNotEmpty;
  }

  bool isAnyTicketSelected() {
    return globals.returnTickets.any((ticket) => ticket.isSelected.value);
  }
}
