import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import '../../../src/globals.dart';
import '../../helpers.dart';
import 'payment_logic.dart';

class PaymentScreen extends StatelessWidget {
  late final PaymentLogic logic;
  final globals = Get.find<Globals>();

  PaymentScreen({Key? key}) : super(key: key) {
    final paymentMethod = Get.arguments?['paymentMethod'];
    logic = Get.put(PaymentLogic(paymentMethod: paymentMethod));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
          width: 1920,
          height: 1080,
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(color: Colors.white),
          child: Obx(() {
            bool isSuccessPaymentOrder = logic.processingSteps[1].processing == StateProcessing.check;
            bool isStartPayment = logic.processingSteps[0].processing == StateProcessing.check;
            bool isSPB = logic.paymentMethod == PaymentMethod.spb;
            bool isCard = logic.paymentMethod == PaymentMethod.card;
            return Stack(
              children: [
                if (isSPB && isStartPayment) centerMessageSPB(),
                if (isSuccessPaymentOrder && isStartPayment) centerSberImage(),
                if (isCard && !isSuccessPaymentOrder && isStartPayment) centerMessageCard(),
                mainScreenButton(),
                rightBlock(),
              ],
            );
          })),
    );
  }

  Positioned rightBlock() {
    return Positioned(
      top: 60,
      right: 60,
      child: Container(
          decoration: BoxDecoration(
            color: Color(0xFFF1F8FF),
            borderRadius: BorderRadius.circular(24),
          ),
          width: 590,
          height: globals.tripOrder.isPaymentProcessingFinished.value ? 964 : 859,
          child: Obx(() {
            return Stack(
              children: [
                if (globals.tripOrder.isPaymentProcessingFinished.value) ...[
                  orderHeaderFinal(),
                  departureBlock(),
                  arrivalBlock(),
                  timeAndDetails(),
                  seatsBlock(),
                  totalAmount(),
                  qrLink(),
                ] else ...[
                  orderHeader(),
                  stateList(),
                  dividerLine(),
                ]
              ],
            );
          })),
    );
  }

  Positioned stateList() {
    return Positioned(
      left: 52,
      top: 225,
      right: 52,
      child: Column(
        spacing: 42,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.end,
        children: logic.processingSteps.map((step) {
          return stateItem(step.processing, step.title);
        }).toList(),
      ),
    );
  }

  Row stateItem(StateProcessing state, String title) {
    return Row(
      spacing: 20,
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: ShapeDecoration(
            color: switch (state) {
              StateProcessing.check => AppColors.primary,
              StateProcessing.load => AppColors.emptyStateListItem,
              StateProcessing.empty => AppColors.emptyStateListItem,
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
          ),
          child: switch (state) {
            StateProcessing.check => Image.asset(
                "lib/assets/images/check.png",
                color: Colors.white,
                scale: 1.2,
              ),
            StateProcessing.load => ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Image.asset("lib/assets/images/load.gif"),
              ),
            StateProcessing.empty => Container(),
          },
        ),
        Text(
          title,
          style: TextStyle(
            color: Colors.black,
            fontSize: 28,
            fontWeight: FontWeight.w700,
          ),
        )
      ],
    );
  }

  Positioned orderHeader() {
    return Positioned(
      left: 127,
      top: 40,
      child: Text(
        'Оформление',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Color(0xFF1E90FF),
          fontSize: 64,
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  Positioned dividerLine() {
    return Positioned(
      left: 52,
      top: 157,
      right: 52,
      child: CustomPaint(
        size: Size(482, 3), // Размер линии
        painter: DottedLinePainter(),
      ),
    );
  }

  Positioned mainScreenButton() {
    return Positioned(
      left: 80,
      top: 941,
      child: MainBackButton(),
    );
  }

  Positioned centerMessageSPB() {
    return Positioned(
      left: 292,
      top: 234,
      child: Column(
        spacing: 39,
        children: [
          Text(
            "Оплата заказа по СБП",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
              fontSize: 48,
              fontFamily: 'Overpass',
              fontWeight: FontWeight.w900,
            ),
          ),
          Text(
            "Для оплаты остканируйте QR-код в приложении\nбанка или камерой телефона",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
              fontSize: 32,
              fontFamily: 'Overpass',
              fontWeight: FontWeight.w500,
            ),
          ),
          Container(
            height: 300,
            width: 300,
            child: Obx(() {
              if (globals.createPayment.value.redirectUrl == null) {
                return Center(child: CircularProgressIndicator());
              } else
                return PrettyQrView.data(
                  data: globals.createPayment.value.redirectUrl.toString(),
                  errorCorrectLevel: QrErrorCorrectLevel.M,
                  decoration: const PrettyQrDecoration(
                    shape: PrettyQrSmoothSymbol(
                      color: Colors.black,
                      roundFactor: 0, // Убираем закругления
                    ),
                    image: PrettyQrDecorationImage(
                      image: AssetImage('lib/assets/images/av.png'),
                    ),
                  ),
                );
            }),
          )
        ],
      ),
    );
  }

  Positioned centerMessageCard() {
    return Positioned(
      left: 292,
      top: 323,
      child: Column(
        spacing: 39,
        children: [
          Text(
            "Следуйте инструкциям\nна банковском терминале",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
              fontSize: 48,
              fontFamily: 'Overpass',
              fontWeight: FontWeight.w900,
            ),
          ),
          Text(
            "Когда мы получим от него данные об оплате,\nи билеты будут подтверждены системой\nавтовокзала, они будут напечатаны.",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
              fontSize: 32,
              fontFamily: 'Overpass',
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Positioned centerSberImage() {
    bool opacity = logic.processingSteps[1].processing == StateProcessing.check;
    return Positioned(
      left: 72,
      top: 58,
      child: AnimatedOpacity(
        opacity: opacity ? 1.0 : 0.0,
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
        child: Container(
          width: 1156,
          height: 859,
          child: Image.asset("lib/assets/images/sber.png"),
        ),
      ),
    );
  }

  /// MARK: - Финайльный стейт
  Positioned orderHeaderFinal() {
    return Positioned(
      left: 90,
      top: 40,
      child: Row(
        spacing: 14,
        children: [
          Image.asset(
            "lib/assets/images/check_2.png",
            scale: 1,
          ),
          Text(
            'Заказ №9001020',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
              fontSize: 40,
              fontWeight: FontWeight.w900,
            ),
          ),
        ],
      ),
    );
  }

  Positioned timeAndDetails() {
    return Positioned(
      left: 52,
      top: 293,
      right: 0,
      child: Container(
        height: 91,
        child: Obx(
          () {
            return Stack(
              children: [
                Positioned(
                  left: 0,
                  top: 0,
                  child: Opacity(
                    opacity: 0.80,
                    child: Text(
                      'Время в пути: ${globals.tripOrder.selectedTripDetail.value.travelTimeHuman ?? ""}',
                      style: TextStyle(
                        color: Color(0xFF3E3B56),
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 0.29,
                  top: 33,
                  child: Opacity(
                    opacity: 0.80,
                    child: Text(
                      'Перевозчик: ${globals.tripOrder.selectedTripDetail.value.carrier ?? ""}',
                      style: TextStyle(
                        color: Color(0xFF3E3B56),
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 0,
                  top: 66,
                  child: Opacity(
                    opacity: 0.80,
                    child: Text(
                      'Автобус: ${globals.tripOrder.selectedTripDetail.value.busInfo ?? ""}',
                      style: TextStyle(
                        color: Color(0xFF3E3B56),
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Positioned arrivalBlock() {
    return Positioned(
      right: 52,
      top: 141,
      child: Obx(
        () {
          return Container(
            width: 297,
            height: 132,
            child: Stack(
              children: [
                Positioned(
                  right: 0,
                  top: 37,
                  child: Text(
                    globals.tripOrder.endTimeString,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 48,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ),
                Positioned(
                  left: 177,
                  top: 0,
                  child: Text(
                    'Прибытие',
                    style: TextStyle(
                      color: Color(0xFF1E90FF),
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                Positioned(
                  right: 0,
                  top: 102,
                  child: Text(
                    "${globals.tripOrder.selectedTripDetail.value.pointB?.title ?? ""}",
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Positioned departureBlock() {
    return Positioned(
      left: 52,
      top: 141,
      child: Obx(
        () {
          return Container(
            width: 258.29,
            height: 132,
            child: Stack(
              children: [
                Positioned(
                  left: 0,
                  top: 37,
                  child: Text(
                    globals.tripOrder.startTimeString,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 48,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ),
                Positioned(
                  left: 0,
                  top: 102,
                  child: Text(
                    globals.tripOrder.selectedTripDetail.value.pointA?.title ?? "",
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                Positioned(
                  left: 0,
                  top: 0,
                  child: Text(
                    'Отправление',
                    style: TextStyle(
                      color: Color(0xFF1E90FF),
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Positioned seatsBlock() {
    return Positioned(
      left: 52,
      top: 384,
      right: 52,
      height: 245,
      child: Obx(() {
        return Column(
          spacing: 8,
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            if (globals.tripOrder.adultsCount.value > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    globals.tripOrder.adultsCount.value.toString() + ' взрослый',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    (globals.tripOrder.adultsCount.value * (globals.tripOrder.selectedTripDetail.value.priceFull ?? 0).toDouble()).toString() + ' ₽',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                ],
              ),
            if (globals.tripOrder.childrenCount.value > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    globals.tripOrder.childrenCount.value.toString() + ' детский',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    (globals.tripOrder.childrenCount.value * (globals.tripOrder.selectedTripDetail.value.priceChild ?? 0).toDouble()).toString() + ' ₽',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                ],
              ),
            if (globals.tripOrder.baggageCount.value > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    globals.tripOrder.baggageCount.value.toString() + ' багажный',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    (globals.tripOrder.baggageCount.value.toDouble() * (globals.tripOrder.selectedTripDetail.value.priceBaggage ?? 0).toDouble()).toString() + ' ₽',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                ],
              ),
            if (globals.tripOrder.noticeInfoMessage.value && (globals.tripOrder.selectedTripDetail.value.noticeInfo?.noticeAvailable ?? false))
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Оповещение об отмене',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    globals.tripOrder.noticeFee.toString() + ' ₽',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                ],
              ),
            if (globals.tripOrder.insuranceFee > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Страхование',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    globals.tripOrder.insuranceFee.toString() + ' ₽',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                ],
              ),
            if (globals.tripOrder.serviceFee > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Сервисный сбор автовокзала',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    globals.tripOrder.serviceFee.toString() + ' ₽',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                ],
              ),

            ///Комиссия за пассажирский билет
            if (globals.tripOrder.commissionTicket > 0)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Комиссия за пассажирский билет',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    globals.tripOrder.commissionTicket.toString() + ' ₽',
                    style: TextStyle(
                      color: Color(0xFF3E3B56),
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                ],
              ),
          ],
        );
      }),
    );
  }

  Widget totalAmount() {
    return Obx(() {
      return Positioned(
        key: ValueKey(globals.tripOrder.selectedTripDetail.value.hashCode),
        left: 52,
        top: 664,
        right: 52,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'ИТОГО:',
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 36,
                fontWeight: FontWeight.w900,
              ),
            ),
            Text(
              globals.tripOrder.totalPrice.toString() + ' ₽',
              style: TextStyle(
                color: Color(0xFF1E90FF),
                fontSize: 36,
                fontWeight: FontWeight.w800,
              ),
            ),
          ],
        ),
      );
    });
  }

  Positioned qrLink() {
    return Positioned(
      left: 52,
      top: 734,
      width: 490,
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 18,
            children: [
              Text(
                'Ссылка на билеты',
                style: TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 24,
                  fontWeight: FontWeight.w800,
                ),
              ),
              Obx(() {
                return Text(
                  'Этот экран закроется\nчерез ${logic.secondsLeft} сек.',
                  style: TextStyle(
                    color: Color(0xFF1E90FF),
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                  ),
                );
              }),
            ],
          ),
          Spacer(),
          Container(
            height: 195,
            width: 195,
            child: globals.tripOrder.createOrder.value.url == null
                ? Center(child: CircularProgressIndicator())
                : PrettyQrView.data(
                    data: globals.tripOrder.createOrder.value.url!,
                    errorCorrectLevel: QrErrorCorrectLevel.M,
                    decoration: const PrettyQrDecoration(
                      shape: PrettyQrSmoothSymbol(
                        color: Colors.black,
                        roundFactor: 0, // Убираем закругления
                      ),
                      image: PrettyQrDecorationImage(
                        image: AssetImage('lib/assets/images/av.png'),
                      ),
                    )),
          ),
        ],
      ),
    );
  }
}
