import 'dart:async';

import 'package:get/get.dart';
import 'package:terminal/models/create_payment_model.dart';
import 'package:terminal/src/api.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/payment/error_payment/error_payment_logic.dart';
import 'package:terminal/ui/payment/error_payment/error_payment_screen.dart';

enum PaymentMethod { card, spb }

enum StateProcessing { check, load, empty }

enum PaymentStep { createOrder, getPayment, confirmPayment, printCheck, successOrder }

class PaymentLogic extends GetxController {
  final globals = Get.find<Globals>();
  final api = Get.find<Api>();
  RxList processingSteps = [].obs;
  late final PaymentMethod paymentMethod;
  Timer? _timer;
  int checkPeriod = 2;
  int timeout = 200;
  int alreadyWaiting = 0;

  /// Для финального стэйта с отсчетом времени до перехода
  var secondsLeft = 60.obs; // Начальное время в секундах

  PaymentLogic({String? paymentMethod}) {
    this.paymentMethod = PaymentMethod.values.byName(paymentMethod ?? 'card');
  }

  @override
  void onInit() {
    super.onInit();
    globals.tripOrder.isPaymentProcessingFinished.value = false;
    processingSteps.add(ProcessingSteps(processing: StateProcessing.load, title: 'Бронируем заказ'));
    processingSteps.add(ProcessingSteps(processing: StateProcessing.empty, title: 'Получаем оплату'));
    processingSteps.add(ProcessingSteps(processing: StateProcessing.empty, title: 'Подтверждаем оплату\nв системе автовокзала'));
    processingSteps.add(ProcessingSteps(processing: StateProcessing.empty, title: 'Печатаем чек'));
    processingSteps.add(ProcessingSteps(processing: StateProcessing.empty, title: 'Заказ успешно оформлен'));
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }

  @override
  void onReady() {
    /// Создаем бронирование или оплачиваем сразу если не оплачен
    if (globals.tripOrder.createOrder.value.isDontPaymentOrder) {
      createPayment();
    } else {
      createOrder();
    }

    super.onReady();
  }

  /// 1. Создание бронирования
  Future<void> createOrder() async {
    final orderToPlace = globals.tripOrder.orderToPlace.value;

    ///Ставим дефолтные значения
    orderToPlace.buyerEmail = orderToPlace.buyerEmail ?? "";
    orderToPlace.buyerPhone = orderToPlace.buyerPhone ?? "";
    orderToPlace.sendSms = globals.tripOrder.noticeInfoMessage.value;

    final result = await api.createOrder(orderToPlace);

    /// Отображаем ошибку
    if (result.isFailure || result.data == null) {
      final message = result.error?.message ?? "Ошибка создания бронирования";
      Get.off(() => ErrorPaymentScreen(ErrorType.fail, message));
      return;
    }

    globals.tripOrder.createOrder.value = result.data!;

    /// Создаем платеж
    createPayment();
  }

  /// 2. Создание платежа
  Future<void> createPayment() async {
    final createOrder = globals.tripOrder.createOrder;

    ///Устанавливаем тип оплаты
    /// "payment_button" при создании платежа передаем id выбранной кнопки или null если это KKM
    final spbButton = globals.tripOrder.selectedTripDetail.value.paymentButtons?.firstWhereOrNull((button) => button.slug == "sbp_qr");
    final paymentButton = paymentMethod == PaymentMethod.card ? null : spbButton?.id;
    final idOrder = createOrder.value.id ?? 0;
    final result = await api.createPayment(idOrder, 1, paymentButton);

    /// Отображаем ошибку
    if (result.isFailure || result.data == null) {
      final message = result.error?.message ?? "Ошибка создания бронирования";
      Get.off(() => ErrorPaymentScreen(ErrorType.fail, message));
      return;
    }

    globals.createPayment.value = result.data!;

    _changeStep(PaymentStep.createOrder);

    /// Если оплата картой выполняем команду на ККМ
    if (paymentMethod == PaymentMethod.card) {
      await executeCommand(result.data?.data);
      _changeStep(PaymentStep.getPayment);
    }

    /// Если оплата по СПБ то начинаем сразу проверять статус оплаты
    confirmPayment();
  }

  /// 3. Выполнение команды на ККМ Сервер
  Future<void> executeCommand(PaymentData? data) async {
    /// Выполняем команду на ККМ
    final executeCommandResult = await api.executeCommand(data);

    final orderId = globals.tripOrder.createOrder.value.id;
    final paymentId = globals.createPayment.value.id;
    if (orderId != null && paymentId != null) {
      /// Отправляем результат на бэк
      final sendPaymentResult = await api.sendPaymentResult(
        orderId,
        paymentId,
        executeCommandResult,
      );

      if (sendPaymentResult.isFailure || sendPaymentResult.data == null) {
        final message = sendPaymentResult.error?.message ?? "Ошибка работы с терминалом оплаты";
        Get.off(() => ErrorPaymentScreen(ErrorType.fail, message));
        return;
      }
    } else {
      final message = "Ошибка работы с терминалом оплаты";
      Get.off(() => ErrorPaymentScreen(ErrorType.fail, message));
    }
  }

  /// 4. Подтверждение оплаты в системе автовокзала
  Future<void> confirmPayment() async {
    final orderId = globals.tripOrder.createOrder.value.id;
    final paymentId = globals.createPayment.value.id;
    if (orderId != null && paymentId != null) {
      /// Проверяем статус оплаты
      try {
        await _startGetStatusPayment(orderId, paymentId);
      } catch (message) {
        Get.off(() => ErrorPaymentScreen(ErrorType.fail, message.toString()));
      }

      if (paymentMethod == PaymentMethod.spb) {
        _changeStep(PaymentStep.getPayment);
      }

      /// Проверяем статус заказа
      try {
        await _startGetStatusOrder(orderId);
      } catch (message) {
        Get.off(() => ErrorPaymentScreen(ErrorType.fail, message.toString()));
      }

      /// Печатаем чек на терминале
      if (globals.tripOrder.createOrder.value.printCommands != null) {
        try {
          final printCommands = globals.tripOrder.createOrder.value.printCommands!;
          for (final printCommand in printCommands) {
            final executeCommandResult = await api.executeCommandPrint(printCommand);
          }
        } catch (message) {
          Get.off(() => ErrorPaymentScreen(ErrorType.fail, message.toString()));
        }
      }

      /// Возврат денег
      if (globals.tripOrder.createOrder.value.paymentReturn != null && globals.tripOrder.createOrder.value.isErrorPaymentOrder) {
        /// Возврат денег по ККМ
        await _returnPayment();

        Get.offAllNamed("/returnInfoPaymentCardScreen");
        return;
      } else if (globals.tripOrder.createOrder.value.isErrorPaymentOrder) {
        /// Возврат денег по СПБ
        Get.offAllNamed("/returnInfoPaymentSpbScreen");
        return;
      }

      _changeStep(PaymentStep.confirmPayment);
      printCheck();
    }
  }

  /// 5. Печатаем чек
  Future<void> printCheck() async {
    _changeStep(PaymentStep.printCheck);
    successOrder();
  }

  /// 6. Заказ успешно оплачен
  Future<void> successOrder() async {
    _changeStep(PaymentStep.successOrder);

    /// Показываем финальный стэйт
    globals.tripOrder.isPaymentProcessingFinished.value = true;
    _startLabelTimer();
  }

  /// Осуществляем возрат денег
  Future<void> _returnPayment() async {
    /// Выполняем команду на ККМ
    final executeCommandResult = await api.executeCommand(globals.tripOrder.createOrder.value.paymentReturn?.data);

    final orderId = globals.tripOrder.createOrder.value.id;
    final paymentId = globals.tripOrder.createOrder.value.paymentReturn?.id;
    if (orderId != null && paymentId != null) {
      /// Отправляем результат на бэк
      final sendPaymentResult = await api.sendPaymentResult(
        orderId,
        paymentId,
        executeCommandResult,
      );

      if (sendPaymentResult.isFailure || sendPaymentResult.data == null) {
        final message = sendPaymentResult.error?.message ?? "Ошибка работы с терминалом оплаты";
        Get.off(() => ErrorPaymentScreen(ErrorType.fail, message));
        return;
      }
    } else {
      final message = "Ошибка работы с терминалом оплаты";
      Get.off(() => ErrorPaymentScreen(ErrorType.fail, message));
    }
  }

  /// Опрашиваем статус оплаты
  Future<void> _startGetStatusPayment(int orderId, int paymentId) async {
    _timer?.cancel();
    alreadyWaiting = 0;

    Completer<void> completer = Completer<void>();

    _timer = Timer.periodic(
      Duration(seconds: checkPeriod),
      (timer) async {
        alreadyWaiting += checkPeriod;
        final statusPayment = await api.getStatusPayment(orderId, paymentId);

        if (completer.isCompleted) return;

        // Отображаем ошибку
        if (statusPayment.isFailure || statusPayment.data == null) {
          final message = statusPayment.error?.message ?? "Ошибка проверки статуса оплаты";
          timer.cancel();
          if (!completer.isCompleted) {
            completer.completeError(Exception(message));
          }
          return;
        }

        globals.createPayment.value = statusPayment.data!;

        if (statusPayment.data?.status?.id == "done") {
          timer.cancel();
          if (!completer.isCompleted) {
            completer.complete();
          }
        }

        if (alreadyWaiting > timeout) {
          timer.cancel();
          //TODO: - Сессия истекла - нужно показать соответсвующий экран
          if (!completer.isCompleted) {
            completer.completeError(Exception());
          }
        }
      },
    );

    return completer.future;
  }

  // Опрашиваем статус заказа
  Future<void> _startGetStatusOrder(int orderId) async {
    _timer?.cancel();
    alreadyWaiting = 0;

    Completer<void> completer = Completer<void>();

    _timer = Timer.periodic(
      Duration(seconds: checkPeriod),
      (timer) async {
        alreadyWaiting += checkPeriod;
        final result = await api.getStatusOrder(orderId);

        // Отображаем ошибку
        if (result.isFailure || result.data == null || result.data?.isErrorPaymentOrder == true) {
          final message = result.error?.message ?? "Ошибка проверки статуса заказа";
          timer.cancel();
          completer.completeError(Exception(message));
          return;
        }

        globals.tripOrder.createOrder.value = result.data!;

        if (globals.tripOrder.createOrder.value.isSuccessPaymentOrder) {
          timer.cancel();
          completer.complete();
        }

        if (alreadyWaiting > timeout) {
          timer.cancel();
          //TODO: - Сессия истекла - нужно показать соответсвующий экран
          completer.completeError(Exception());
        }
      },
    );

    return completer.future;
  }

  void _changeStep(PaymentStep paymentStep) {
    int currentStepsIndex = 0;

    switch (paymentStep) {
      case PaymentStep.createOrder:
        currentStepsIndex = 0;
        break;
      case PaymentStep.getPayment:
        currentStepsIndex = 1;
        break;
      case PaymentStep.confirmPayment:
        currentStepsIndex = 2;
        break;
      case PaymentStep.printCheck:
        currentStepsIndex = 3;
        break;
      case PaymentStep.successOrder:
        currentStepsIndex = 4;
        break;
    }

    processingSteps[currentStepsIndex].processing = StateProcessing.check;
    processingSteps.refresh();
    if (currentStepsIndex < 4) {
      currentStepsIndex += 1;
      processingSteps[currentStepsIndex].processing = StateProcessing.load;
    }
  }

  /// Показываем отсавшееся время до перехода на главный экран
  void _startLabelTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (secondsLeft.value > 0) {
        secondsLeft.value--;
      } else {
        timer.cancel();
        globals.resetAllData();
        Get.offAllNamed("/mainScreen");
      }
    });
  }
}

class ProcessingSteps {
  final String title;
  StateProcessing processing;

  ProcessingSteps({
    required this.title,
    required this.processing,
  });
}
