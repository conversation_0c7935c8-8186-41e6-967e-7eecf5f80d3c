// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:get/get.dart';

import 'package:terminal/src/globals.dart';

enum ErrorType { fail, session, failReturn }

class ErrorPaymentLogic extends GetxController {
  final globals = Get.find<Globals>();
  ErrorType typePayment;
  String? errorMessage;

  String? get shortMessage {
    if (errorMessage == null) return null;
    return errorMessage!.length <= 150 ? errorMessage! : errorMessage!.substring(0, 150);
  }

  ErrorPaymentLogic({required this.typePayment, this.errorMessage}) {
    this.typePayment = typePayment;
    this.errorMessage = errorMessage;
  }

  @override
  void onInit() {
    super.onInit();
  }
}
