// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';
import 'package:terminal/ui/payment/payment/payment_logic.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';

import '../../../src/globals.dart';
import 'error_payment_logic.dart';

class ErrorPaymentScreen extends StatelessWidget {
  late final ErrorPaymentLogic logic;
  final globals = Get.find<Globals>();

  ErrorPaymentScreen(ErrorType typePayment, String errorMessage) {
    logic = Get.put(ErrorPaymentLogic(typePayment: typePayment, errorMessage: errorMessage));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: 1920,
        height: 1080,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(color: Colors.white),
        child: Stack(
          children: [
            centerMessage(),
            mainScreenButton(),
            if (logic.typePayment == ErrorType.fail) buttonsFail(),
            if (logic.typePayment == ErrorType.failReturn) buttonsReturn(),
          ],
        ),
      ),
    );
  }

  Positioned mainScreenButton() {
    return Positioned(
      left: 80,
      top: 941,
      child: MainBackButton(),
    );
  }

  Positioned centerMessage() {
    String title;
    switch (logic.typePayment) {
      case ErrorType.fail:
        title = "Ошибка";
        break;
      case ErrorType.session:
        title = "Сессия истекла";
        break;
      case ErrorType.failReturn:
        title = "Ошибка возврата";
        break;
    }

    String subTitle;
    switch (logic.typePayment) {
      case ErrorType.fail:
        subTitle = logic.shortMessage ?? "Неизвестная ошибка";
        break;
      case ErrorType.session:
        subTitle = "Ваша сессия истекла. Попробуйте еще раз.";
        break;
      case ErrorType.failReturn:
        final startErrorText = logic.shortMessage ?? "При оформлении возврата произошла ошибка.";
        subTitle = "$startErrorText \n\nВы можете попробовать провести возврат еще раз или обратиться в техподдержку.";
        break;
    }

    final isLong = logic.shortMessage != logic.errorMessage;

    return Positioned(
      left: 542,
      top: 80,
      width: 900,
      child: Column(
        spacing: 39,
        children: [
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
              fontSize: 72,
              fontFamily: 'Overpass',
              fontWeight: FontWeight.w800,
            ),
          ),
          Column(
            spacing: 16,
            children: [
              Text(
                subTitle,
                textAlign: TextAlign.left,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 40,
                  fontFamily: 'Overpass',
                  fontWeight: FontWeight.w500,
                ),
              ),

              /// Показываем всю ошибку
              if (isLong)
                TextButton(
                  onPressed: () {
                    showDialog(
                      context: Get.context!,
                      builder: (_) => AlertDialog(
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                        title: Text("Подробности ошибки"),
                        content: SingleChildScrollView(
                          child: Text(
                            logic.errorMessage ?? "Неизвестная ошибка",
                            style: TextStyle(fontSize: 32),
                          ),
                        ),
                        actions: [
                          TextButton(
                            child: Text("Закрыть"),
                            onPressed: () => Navigator.of(Get.context!).pop(),
                          ),
                        ],
                      ),
                    );
                  },
                  child: Text(
                    "Подробнее",
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ),
            ],
          ),

          /// Картинка при ошибке истечения сессии
          if (logic.typePayment == ErrorType.session)
            Column(
              children: [
                SizedBox(height: 53),
                Image.asset(
                  'lib/assets/images/undraw_cancel_re_pkdm.png',
                  width: 415,
                ),
              ],
            ),
        ],
      ),
    );
  }

  /// Кнопки при неуспешном возврате билетов
  Positioned buttonsReturn() {
    return Positioned(
      left: 592,
      top: 640,
      child: Container(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// Техподдержка
            supportButton(),
            const SizedBox(width: 24),

            /// Попробовать еще раз
            CustomInkWell(
              borderRadius: 12,
              color: AppColors.primary,
              onTap: () {
                Get.toNamed('/returnTicketChooseScreen');
              },
              child: Container(
                padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Попробовать еще раз',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 36,
                        fontFamily: 'Overpass',
                        fontWeight: FontWeight.w900,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Кнопки при неуспешной оплате
  Positioned buttonsFail() {
    return Positioned(
      left: 422,
      top: 640,
      child: Container(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// Техподдержка
            supportButton(),
            const SizedBox(width: 24),

            /// Оплатить картой
            CustomInkWell(
              borderRadius: 12,
              color: Color(0xFFF1F8FF),
              onTap: () {
                Get.toNamed("/paymentScreen", arguments: {'paymentMethod': PaymentMethod.card.name});
              },
              child: Container(
                padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Оплатить картой',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 36,
                        fontFamily: 'Overpass',
                        fontWeight: FontWeight.w900,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 24),

            /// Оплатить по СБП
            CustomInkWell(
              borderRadius: 12,
              color: AppColors.primary,
              onTap: () {
                Get.toNamed("/paymentScreen", arguments: {'paymentMethod': PaymentMethod.spb.name});
              },
              child: Container(
                padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Оплатить по СБП',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 36,
                        fontFamily: 'Overpass',
                        fontWeight: FontWeight.w900,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Кнопка техподержка
  Widget supportButton() {
    return CustomInkWell(
      borderRadius: 12,
      color: Color(0xFFF1F8FF),
      onTap: () {
        //TODO: - Доделать переход
        print("Переход в техподдержку");
        //Get.toNamed('/');
      },
      child: Container(
        padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Техподдержка',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 36,
                fontFamily: 'Overpass',
                fontWeight: FontWeight.w900,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
