import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';
import 'package:terminal/ui/payment/payment/payment_logic.dart';
import 'package:terminal/ui/payment/return_process/return_process_logic.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';

class ReturnProcessScreen extends StatelessWidget {
  final logic = Get.put(ReturnProcessLogic());
  final globals = Get.find<Globals>();

  ReturnProcessScreen({Key? key}) : super(key: key) {}

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
          width: 1920,
          height: 1080,
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(color: Colors.white),
          child: Obx(() {
            return Stack(
              children: [
                centerSberImage(),
                mainScreenButton(),
                rightBlock(),
              ],
            );
          })),
    );
  }

  Widget rightBlock() {
    return Positioned(
      top: 60,
      right: 60,
      child: Container(
          decoration: BoxDecoration(
            color: Color(0xFFF1F8FF),
            borderRadius: BorderRadius.circular(24),
          ),
          width: 590,
          height: globals.tripOrder.isPaymentProcessingFinished.value ? 964 : 859,
          child: Obx(() {
            return Stack(
              children: [
                orderHeader(),
                stateList(),
                dividerLine(),
                if (logic.isFinishReturn.value) labelCloseTimer(),
              ],
            );
          })),
    );
  }

  Widget stateList() {
    return Positioned(
      left: 52,
      top: 225,
      right: 44,
      child: Column(
        spacing: 42,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.end,
        children: logic.processingSteps.map((step) {
          return stateItem(step.processing, step.title);
        }).toList(),
      ),
    );
  }

  Widget stateItem(StateProcessing state, String title) {
    return Row(
      spacing: 20,
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: ShapeDecoration(
            color: switch (state) {
              StateProcessing.check => AppColors.primary,
              StateProcessing.load => AppColors.emptyStateListItem,
              StateProcessing.empty => AppColors.emptyStateListItem,
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
          ),
          child: switch (state) {
            StateProcessing.check => Image.asset(
                "lib/assets/images/check.png",
                color: Colors.white,
                scale: 1.2,
              ),
            StateProcessing.load => ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Image.asset("lib/assets/images/load.gif"),
              ),
            StateProcessing.empty => Container(),
          },
        ),
        Text(
          title,
          style: TextStyle(
            color: Colors.black,
            fontSize: 28,
            fontWeight: FontWeight.w700,
          ),
        )
      ],
    );
  }

  Widget orderHeader() {
    return Obx(() {
      return Positioned(
        left: logic.isFinishReturn.value ? 186 : 85,
        top: 40,
        child: Text(
          logic.isFinishReturn.value ? 'Готово' : 'Оформление',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Color(0xFF1E90FF),
            fontSize: 64,
            fontWeight: FontWeight.w800,
          ),
        ),
      );
    });
  }

  Widget dividerLine() {
    return Positioned(
      left: 52,
      top: 157,
      right: 52,
      child: CustomPaint(
        size: Size(482, 3),
        painter: DottedLinePainter(),
      ),
    );
  }

  Widget mainScreenButton() {
    return Positioned(
      left: 80,
      top: 941,
      child: MainBackButton(),
    );
  }

  Widget centerSberImage() {
    return Positioned(
      left: 72,
      top: 58,
      child: Container(
        width: 1156,
        height: 859,
        child: Image.asset("lib/assets/images/sber.png"),
      ),
    );
  }

  Widget labelCloseTimer() {
    return Positioned(
      left: 87,
      top: 798,
      child: Obx(() {
        return Text(
          'Этот экран закроется через ${logic.secondsLeft} сек.',
          style: TextStyle(
            color: Color(0xFF1E90FF),
            fontSize: 24,
            fontWeight: FontWeight.w700,
          ),
        );
      }),
    );
  }
}
