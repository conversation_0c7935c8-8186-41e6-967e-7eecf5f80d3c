import 'dart:async';

import 'package:get/get.dart';
import 'package:terminal/models/create_order_model.dart';
import 'package:terminal/src/api.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/payment/error_payment/error_payment_logic.dart';
import 'package:terminal/ui/payment/error_payment/error_payment_screen.dart';
import 'package:terminal/ui/payment/payment/payment_logic.dart';

enum ReturnStep { returnTicket, returnPayment, successReturn }

class ReturnProcessLogic extends GetxController {
  final globals = Get.find<Globals>();
  final api = Get.find<Api>();
  Timer? _timer;
  RxList processingSteps = [].obs;
  final isFinishReturn = false.obs;

  /// Для финального стэйта с отсчетом времени до перехода
  var secondsLeft = 60.obs; // Начальное время в секундах

  @override
  void onInit() {
    super.onInit();
    isFinishReturn.value = false;

    processingSteps.add(ProcessingSteps(processing: StateProcessing.load, title: 'Возврат билетов\nв системе АВ'));
    final firstReturnTicket = globals.returnTickets.value.where((ticket) => (ticket.isSelected.value ?? false)).first;
    if (firstReturnTicket.paymentButton?.slug == "sbp_qr") {
      processingSteps.add(ProcessingSteps(processing: StateProcessing.empty, title: 'Возврат денежных средств'));
    } else {
      processingSteps.add(ProcessingSteps(processing: StateProcessing.empty, title: 'Возврат денежных средств\nПриложите карту'));
    }
    processingSteps.add(ProcessingSteps(processing: StateProcessing.empty, title: 'Возврат успешно проведен'));
  }

  @override
  void onClose() {}

  @override
  void onReady() {
    returnTicket();

    super.onReady();
  }

  /// 1. Возврат билетов в системе АВ
  Future<void> returnTicket() async {
    final returnTickets = globals.returnTickets.value.where((ticket) => (ticket.isSelected.value ?? false)).toList();
    List<String> ticketNumbers = returnTickets.map((ticket) => ticket.ticketNumber).whereType<String>().toList();

    final result = await api.ticketsReturn(ticketNumbers);

    /// Отображаем ошибку
    if (result.isFailure || result.data == null) {
      final message = result.error?.message ?? "Ошибка возврата билета";
      Get.off(() => ErrorPaymentScreen(ErrorType.failReturn, message));
      return;
    }

    if (result.data != null) {
      _changeStep(ReturnStep.returnTicket);
      await returnPayment(result.data!);
    } else {
      final message = "Ошибка возврата билета";
      Get.off(() => ErrorPaymentScreen(ErrorType.failReturn, message));
      return;
    }
  }

  /// 2. Возврат денежных средств
  Future<void> returnPayment(CreateOrderModel returnOrder) async {
    if (returnOrder.id != null) {
      final result = await api.createPayment(returnOrder.id!, 2, null);

      /// Отображаем ошибку
      if (result.isFailure || result.data == null) {
        final message = result.error?.message ?? "Ошибка возврата билета";
        Get.off(() => ErrorPaymentScreen(ErrorType.failReturn, message));
        return;
      }

      final orderId = returnOrder.id;

      /// Если СБП то сразу переходим на успешный экран
      if (result.data?.status?.id == "done") {
        _changeStep(ReturnStep.returnPayment);
        await successReturn(orderId);
        return;
      }

      /// Если по терминалу
      if (result.data?.data != null) {
        /// Выполняем команду на ККМ
        final executeCommandResult = await api.executeCommand(result.data!.data);

        final paymentId = result.data?.id;
        if (orderId != null && paymentId != null) {
          /// Отправляем результат на бэк
          final sendPaymentResult = await api.sendPaymentResult(
            orderId,
            paymentId,
            executeCommandResult,
          );

          if (sendPaymentResult.isFailure || sendPaymentResult.data == null || !sendPaymentResult.data!.isSuccess) {
            final message = sendPaymentResult.error?.message ?? "Ошибка работы с терминалом оплаты";
            Get.off(() => ErrorPaymentScreen(ErrorType.fail, message));
            return;
          }

          _changeStep(ReturnStep.returnPayment);

          successReturn(orderId);
        }
      }
    }
  }

  /// 3. Возврат успешно проведен
  Future<void> successReturn(int? orderId) async {
    if (orderId != null) {
      //Получаем чеки для печати
      final result = await api.getStatusOrder(orderId);

      // Отображаем ошибку
      if (result.isFailure || result.data == null || result.data?.isErrorPaymentOrder == true) {
        final message = result.error?.message ?? "Ошибка проверки статуса заказа";
        Get.off(() => ErrorPaymentScreen(ErrorType.fail, message));
        return;
      }

      /// Печатаем чек на терминале
      if (result.data?.printCommands != null) {
        try {
          final printCommands = result.data!.printCommands!;
          for (final printCommand in printCommands) {
            final executeCommandResult = await api.executeCommandPrint(printCommand);
          }
        } catch (message) {
          Get.off(() => ErrorPaymentScreen(ErrorType.fail, message.toString()));
        }
      }
    }

    _changeStep(ReturnStep.successReturn);
    isFinishReturn.toggle();
    _startLabelTimer();
  }

  /// Показываем отсавшееся время до перехода на главный экран
  void _startLabelTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (secondsLeft.value > 0) {
        secondsLeft.value--;
      } else {
        timer.cancel();
        globals.resetAllData();
        Get.offAllNamed("/mainScreen");
      }
    });
  }

  /// Изменяем шаг
  void _changeStep(ReturnStep paymentStep) {
    int currentStepsIndex = 0;

    switch (paymentStep) {
      case ReturnStep.returnTicket:
        currentStepsIndex = 0;
        break;
      case ReturnStep.returnPayment:
        currentStepsIndex = 1;
        break;
      case ReturnStep.successReturn:
        currentStepsIndex = 2;
        break;
    }

    processingSteps[currentStepsIndex].processing = StateProcessing.check;
    processingSteps.refresh();
    if (currentStepsIndex < 2) {
      currentStepsIndex += 1;
      processingSteps[currentStepsIndex].processing = StateProcessing.load;
    }
  }
}
