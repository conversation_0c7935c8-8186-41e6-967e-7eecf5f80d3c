import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/payment/payment/payment_logic.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import '../../../src/globals.dart';
import 'return_info_payment_logic.dart';

class ReturnInfoPaymentScreen extends StatelessWidget {
  final ReturnInfoPaymentLogic logic = Get.put(ReturnInfoPaymentLogic());
  final globals = Get.find<Globals>();
  PaymentMethod typePayment = PaymentMethod.card;

  ReturnInfoPaymentScreen({
    super.key,
    required PaymentMethod typePayment,
  }) {
    this.typePayment = typePayment;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: 1920,
        height: 1080,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(color: Colors.white),
        child: Stack(
          children: [
            centerMessage(),
            mainScreenButton(),
          ],
        ),
      ),
    );
  }

  Positioned mainScreenButton() {
    return Positioned(
      left: 80,
      top: 941,
      child: MainBackButton(),
    );
  }

  Positioned centerMessage() {
    return Positioned(
      left: 542,
      top: 80,
      child: Column(
        spacing: 39,
        children: [
          Text(
            typePayment == PaymentMethod.card ? "Возврат средств" : "Возврат средств по СБП",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
              fontSize: 72,
              fontFamily: 'Overpass',
              fontWeight: FontWeight.w800,
            ),
          ),
          Text(
            typePayment == PaymentMethod.card
                ? "Произошла ошибка при формировании\nзаказа. Приложите карту к пинпаду для\nполного возврата средств."
                : "Произошла ошибка при формировании\nзаказа. Средства вернутся на ваш счет\nавтоматически.",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
              fontSize: 40,
              fontFamily: 'Overpass',
              fontWeight: FontWeight.w500,
            ),
          ),
          if (typePayment == PaymentMethod.card)
            Column(
              children: [
                SizedBox(height: 53),
                Image.asset(
                  'lib/assets/images/card.png',
                  width: 560,
                ),
              ],
            ),
          if (typePayment == PaymentMethod.spb)
            Column(
              children: [
                SizedBox(height: 93),
                Image.asset(
                  'lib/assets/images/sbp.png',
                  width: 300,
                ),
              ],
            ),
        ],
      ),
    );
  }
}
