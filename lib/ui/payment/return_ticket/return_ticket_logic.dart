import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:terminal/src/api.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/helpers.dart';

class ReturnTicketLogic extends GetxController {
  final globals = Get.find<Globals>();
  final api = Get.find<Api>();

  TextEditingController numberController = TextEditingController();
  FocusNode numberFocusNode = FocusNode();
  final isNumberFocused = false.obs;
  final currentTextController = TextEditingController().obs;
  final currentFocusNode = FocusNode().obs;

  @override
  void onInit() {
    super.onInit();

    numberFocusNode.addListener(() {
      isNumberFocused.value = numberFocusNode.hasFocus;
      currentTextController.value = numberController;
      currentFocusNode.value = numberFocusNode;
    });
  }

  @override
  void onClose() {
    numberFocusNode.dispose();
  }

  Future<void> getTicketsToReturn() async {
    try {
      final number = numberController.text;
      final result = await api.getTicketsToReturn(number);

      /// Отображаем ошибку
      if (result.isFailure || result.data == null) {
        final errorMessage = result.error?.message ?? "Ошибка поиска билета";
        WarningHandler.showWarning("Упс!", errorMessage);
        globals.dataState.value = DataLoadingState.LOADED;
        return;
      }

      globals.returnTickets.value = result.data!;

      //Устанавливаем выбранным билет который ввели в поиске
      globals.returnTickets.forEach((ticket) {
        if (ticket.ticketNumber == number && ticket.isReturned == false) {
          ticket.isSelected.value = true;
        }
      });
      Get.toNamed('/returnTicketChooseScreen');
    } catch (e) {
      WarningHandler.showWarning("Упс!", "Произошла ошибка при поиске билета");
      globals.dataState.value = DataLoadingState.LOADED;
    }
  }
}
