import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';
import 'package:terminal/ui/payment/return_ticket/return_ticket_logic.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';

///Возврат билета
class ReturnTicketScreen extends StatelessWidget {
  ReturnTicketScreen({Key? key}) : super(key: key);

  final logic = Get.put(ReturnTicketLogic());
  final globals = Get.find<Globals>();
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey,
        body: Form(
          key: _formKey,
          child: Container(
              width: 1920,
              height: 1080,
              color: Colors.white,
              child: <PERSON>ack(children: [
                BackOrMainButton(),
                Positioned(
                  top: 80,
                  left: 175,
                  right: 175,
                  child: Text(
                    'Возврат билета',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 64,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ),
                Positioned(
                  top: 250,
                  left: 60,
                  right: 60,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    spacing: 18,
                    children: [
                      Container(
                        width: 590,
                        height: 150,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: TextFormField(
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Поле не может быть пустым';
                                  }
                                  return null;
                                },
                                autofocus: true,
                                decoration: InputDecoration(
                                  hintText: "",
                                  hintStyle: TextStyle(color: Colors.grey),
                                  focusedBorder: UnderlineInputBorder(
                                    borderSide: BorderSide(color: AppColors.primary, width: 2),
                                  ),
                                  enabledBorder: UnderlineInputBorder(
                                    borderSide: BorderSide(color: AppColors.enabledBorder, width: 2),
                                  ),
                                ),
                                cursorColor: Colors.black,
                                cursorHeight: 64,
                                cursorWidth: 2,
                                cursorRadius: Radius.zero,
                                onTapOutside: (event) {
                                  FocusScope.of(context).requestFocus(logic.numberFocusNode);
                                },
                                showCursor: true,
                                focusNode: logic.numberFocusNode,
                                controller: logic.numberController,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 64,
                                  fontWeight: FontWeight.w400,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            Obx(() {
                              return Text(
                                "Номер билета",
                                style: TextStyle(
                                  color: logic.isNumberFocused.value ? AppColors.primary : AppColors.enabledBorder,
                                  fontSize: 36,
                                  fontWeight: FontWeight.w100,
                                ),
                              );
                            }),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Obx(() {
                  return Positioned(bottom: 0, left: 0, right: 0, child: Keyboard(logic.currentTextController.value));
                }),
                backMainButton(),
                nextButton()
              ])),
        ));
  }

  Widget backMainButton() {
    return Positioned(
      left: 68,
      bottom: 446,
      child: MainBackButton(),
    );
  }

  Widget nextButton() {
    return Positioned(
      bottom: 446,
      right: 61,
      child: Opacity(
        opacity: 1,
        child: InkWell(
          onTap: () {
            if (_formKey.currentState!.validate()) {
              logic.getTicketsToReturn();
            }
          },
          child: Container(
            width: 180,
            height: 82,
            padding: const EdgeInsets.only(
              top: 20,
              left: 30,
              right: 30,
              bottom: 16,
            ),
            clipBehavior: Clip.antiAlias,
            decoration: ShapeDecoration(
              color: Color(0xFF1E90FF),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Далее',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.w900,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
