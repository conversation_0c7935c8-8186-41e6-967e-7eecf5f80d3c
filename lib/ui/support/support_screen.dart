import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';

class SupportScreen extends StatelessWidget {
  final globals = Get.find<Globals>();

  SupportScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey,
      body: Container(
        width: 1920,
        height: 1080,
        color: Colors.white,
        child: Stack(
          children: [
            BackOrMainButton(),
            title(),
            supportInfo(),
            mainScreenButton(),
          ],
        ),
      ),
    );
  }

  /// Заголовок экрана
  Widget title() {
    return Positioned(
      top: 80,
      left: 175,
      right: 175,
      child: Text(
        'Служба поддержки',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.black,
          fontSize: 64,
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  /// Информация о поддержке
  Widget supportInfo() {
    return Positioned(
      top: 250,
      left: 150,
      right: 150,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Изображение слева
          Container(
            width: 600,
            height: 600,
            child: Image.asset(
              'lib/assets/images/support_image.png',
              fit: BoxFit.contain,
            ),
          ),
          SizedBox(width: 80),
          // Текстовая информация справа
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Если у вас возникли вопросы, вы можете\nобратиться в поддержку по телефону',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 42,
                    fontWeight: FontWeight.w400,
                    height: 1.3,
                  ),
                ),
                SizedBox(height: 40),
                Text(
                  '+7 800 777 43 01',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: 56,
                    fontWeight: FontWeight.w900,
                    letterSpacing: 2,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Кнопка "На главный экран"
  Widget mainScreenButton() {
    return Positioned(
      left: 761,
      bottom: 100,
      child: MainBackButton(),
    );
  }
}
