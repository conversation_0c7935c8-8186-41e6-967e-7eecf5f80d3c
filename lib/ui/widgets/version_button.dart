import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:terminal/src/api.dart';
import 'package:terminal/ui/colors.dart';

class VersionButton {
  static OverlayEntry? _entry;

  static void show(BuildContext context) {
    if (_entry != null) return;

    _entry = OverlayEntry(
      builder: (context) => Positioned(
        top: 20,
        right: 70,
        child: SafeArea(
          child: TextButton(
            onPressed: () {
              Get.to(() => TalkerScreen(talker: talker));
            },
            child: Text(
              "Версия 1.1.3",
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_entry!);
  }

  static void hide() {
    _entry?.remove();
    _entry = null;
  }
}
