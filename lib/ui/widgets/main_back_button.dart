import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';
import 'package:terminal/ui/widgets/confirm_dialog.dart';

class MainBackButton extends StatelessWidget {
  final globals = Get.find<Globals>();

  MainBackButton({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomInkWell(
      color: Color(0xFFF1F8FF),
      splashColor: AppColors.textSecondary,
      borderRadius: 12,
      width: 398,
      height: 82,
      onTap: () async {
        final result = await Get.dialog(Dialog(
            insetAnimationCurve: Curves.easeIn,
            insetAnimationDuration: const Duration(milliseconds: 50),
            insetPadding: EdgeInsets.all(8),
            child:
                confirmDialog("Вы уверены?", "Если вы покинете экран сейчас, внесённые изменения не сохранятся. Продолжить?", "Да, перейти", "Нет, остаться")));

        if (result == true) {
          globals.resetAllData();
          Get.offAllNamed("/mainScreen");
        }
      },
      child: Container(
        width: 398,
        height: 82,
        padding: const EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 16),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'На главный экран',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 36,
                fontWeight: FontWeight.w900,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
