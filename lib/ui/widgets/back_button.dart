import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';

class BackOrMainButton extends StatelessWidget {
  final globals = Get.find<Globals>();

  BackOrMainButton({
    super.key,
    this.additionalAction = null,
  });
  final Function? additionalAction;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 60,
      top: 80,
      child: CustomInkWell(
        color: Color(0xFFF1F8FF),
        splashColor: AppColors.textSecondary,
        borderRadius: 32,
        width: 76,
        height: 76,
        onTap: () {
          if (Get.previousRoute.isEmpty || Get.previousRoute == "/mainScreen") {
            globals.resetAllData();
            Get.offAllNamed('/mainScreen');
          } else {
            Get.back();
          }
          additionalAction?.call();
        },
        child: Container(
          width: 76,
          height: 76,
          decoration: ShapeDecoration(
            color: Color(0x191E90FF),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
          ),
          child: Center(
              child: Image.asset(
            "lib/assets/images/back.png",
            scale: 1.85,
          )),
        ),
      ),
    );
  }
}
