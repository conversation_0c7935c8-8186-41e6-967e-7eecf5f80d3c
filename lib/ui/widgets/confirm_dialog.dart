import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/helpers.dart';

Widget confirmDialog(String title, String message, String confirmButton, String canselButton) {
  return Container(
    width: 900,
    height: 420,
    clipBehavior: Clip.antiAlias,
    decoration: ShapeDecoration(
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(32),
      ),
    ),
    child: Stack(
      children: [
        /// Текст заголовка
        Positioned(
          left: 60,
          top: 60,
          width: 780,
          child: Center(
            child: Text(
              title,
              style: TextStyle(
                color: Colors.black,
                fontSize: 36,
                fontWeight: FontWeight.w800,
              ),
            ),
          ),
        ),

        /// Текст подзаголовка
        Positioned(
          left: 60,
          top: 130,
          width: 780,
          child: Center(
            child: Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 36,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),

        /// Кнопка согласия
        Positioned(
          left: 60,
          top: 280,
          width: 390,
          child: CustomInkWell(
            color: AppColors.primary,
            splashColor: AppColors.primarySplashColor,
            borderRadius: 12,
            width: 398,
            height: 92,
            onTap: () async {
              Get.back(result: true);
            },
            child: Container(
              width: 398,
              height: 92,
              padding: const EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    confirmButton,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 36,
                      fontWeight: FontWeight.w900,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        /// Кнопка отмены
        Positioned(
          left: 460,
          top: 280,
          width: 390,
          child: CustomInkWell(
            color: Color(0xFFF1F8FF),
            splashColor: AppColors.textSecondary,
            borderRadius: 12,
            width: 398,
            height: 92,
            onTap: () async {
              Get.back(result: false);
            },
            child: Container(
              width: 398,
              height: 82,
              padding: const EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    canselButton,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 36,
                      fontWeight: FontWeight.w900,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    ),
  );
}
