import 'package:get/get.dart';
import '../../models/trip_model.dart';
import '../../src/api.dart';
import '../../src/globals.dart';

class SelectTripLogic extends GetxController {
  final api = Get.find<Api>();
  final globals = Get.find<Globals>();

  final trips = <Trip>[].obs;
  final selectedTrip = Trip().obs;
  final dataState = DataLoadingState.LOADING.obs;

  @override
  void onInit() async {
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    dataState.value = DataLoadingState.LOADING;

    if (globals.selectedStationFrom.value.id == null || globals.selectedStationTo.value.id == null) {
      dataState.value = DataLoadingState.ERROR;
      return;
    }

    final fromId = globals.selectedStationFrom.value.id!;
    final toId = globals.selectedStationTo.value.id!;
    final date = globals.selectedDate.value;

    try {
      final res = await api.getTrips(fromId, toId, date);
      trips.value = res;
      dataState.value = DataLoadingState.LOADED;
    } catch (_) {
      dataState.value = DataLoadingState.ERROR;
    }
  }
}
