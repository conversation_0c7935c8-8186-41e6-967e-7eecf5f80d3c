import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:terminal/ui/colors.dart';
import 'package:terminal/ui/widgets/back_button.dart';
import 'package:terminal/ui/widgets/main_back_button.dart';
import '../../models/trip_model.dart';
import '../../src/globals.dart';
import '../helpers.dart';
import 'select_route_logic.dart';

//MARK: - Экран  "Выберите подходящий рейс"
class SelectTripScreen extends StatelessWidget {
  SelectTripScreen({Key? key}) : super(key: key);

  final logic = Get.put(SelectTripLogic());
  final globals = Get.find<Globals>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: 1920,
        height: 1080,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(color: Colors.white),
        child: <PERSON>ack(
          children: [
            Title(),
            Departure(),
            Arrival(),
            Obx(() {
              if (logic.dataState.value == DataLoadingState.LOADING) {
                return ShimerTrip();
              }

              if (logic.dataState.value == DataLoadingState.LOADED && logic.trips.isEmpty) {
                return Container(
                  child: Center(
                    child: Text(
                      'К сожалению, на выбранную дату и направление подходящих рейсов не найдено.\nПопробуйте изменить параметры поиска или выберите другую дату.',
                      style: TextStyle(fontSize: 28),
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              }

              return Positioned(
                top: 291,
                right: 60,
                left: 60,
                bottom: 150,
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    Column(
                      children: logic.trips.map((trip) => OneTrip(trip, globals)).toList(),
                    ),
                  ],
                ),
              );
            }),
            Positioned(
              left: 70,
              bottom: 57,
              child: MainBackButton(),
            ),
            BackOrMainButton(),
          ],
        ),
      ),
    );
  }
}

class NextButton extends StatelessWidget {
  const NextButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 57,
      right: 60,
      child: Opacity(
        opacity: 0.30,
        child: CustomInkWell(
          color: AppColors.primary,
          splashColor: AppColors.primarySplashColor,
          borderRadius: 12,
          onTap: () {
            print('Переход');
          },
          child: Container(
            width: 178,
            height: 82,
            padding: const EdgeInsets.only(top: 20, left: 30, right: 30, bottom: 16),
            clipBehavior: Clip.antiAlias,
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Далее',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.w900,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class OneTrip extends StatelessWidget {
  const OneTrip(this.trip, this.globals);

  final Trip trip;
  final Globals globals;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 18.0),
      child: Container(
        // height: 158,
        padding: EdgeInsets.all(30),
        decoration: BoxDecoration(
          color: Color(0xFFF1F8FF),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 500,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          trip.startTimeString,
                          style: TextStyle(
                            color: Color(0xFF1E90FF),
                            fontSize: 36,
                            fontWeight: FontWeight.w900,
                          ),
                        ),
                        Text(
                          trip.pointA?.title ?? '',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 28,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        Text(
                          (trip.carrier ?? '') + ', ' + (trip.busInfo ?? ''),
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 20,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 350,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          trip.route ?? '',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Text(
                          'Свободных мест: ${trip.freePlace}',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 24,
                            fontWeight: FontWeight.w400,
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 350,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          trip.endTimeString,
                          style: TextStyle(
                            color: Color(0xFF1E90FF),
                            fontSize: 36,
                            fontWeight: FontWeight.w900,
                          ),
                        ),
                        Text(
                          trip.pointB?.title ?? '',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 28,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        Text(
                          trip.travelTimeHuman ?? '',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 20,
                            fontWeight: FontWeight.w400,
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: 30,
            ),
            Row(
              children: [
                Container(
                  width: 196,
                  height: 112,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Color(0xFF1E90FF),
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      '${trip.priceFull}₽',
                      style: TextStyle(
                        color: Color(0xFF1E90FF),
                        fontSize: 36,
                        fontWeight: FontWeight.w900,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                CustomInkWell(
                    color: AppColors.primary,
                    splashColor: AppColors.primarySplashColor,
                    borderRadius: 16,
                    onTap: () {
                      globals.tripOrder.selectedTrip.value = trip;
                      Get.toNamed('/selectTripDetailsScreen');
                    },
                    child: Container(
                      width: 214,
                      height: 112,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Center(
                        child: Text(
                          'Выбрать',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 36,
                            fontWeight: FontWeight.w900,
                          ),
                        ),
                      ),
                    )),
              ],
            )
          ],
        ),
      ),
    );
  }
}

class Arrival extends StatelessWidget {
  const Arrival({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 1216,
      top: 233,
      child: Text(
        'Прибытие',
        textAlign: TextAlign.right,
        style: TextStyle(
          color: Colors.black,
          fontSize: 32,
          fontWeight: FontWeight.w900,
        ),
      ),
    );
  }
}

class Departure extends StatelessWidget {
  const Departure({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 91,
      top: 233,
      child: Text(
        'Отправление',
        style: TextStyle(
          color: Colors.black,
          fontSize: 32,
          fontWeight: FontWeight.w900,
        ),
      ),
    );
  }
}

class Title extends StatelessWidget {
  const Title({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 498,
      top: 80,
      child: Text(
        'Выберите подходящий рейс',
        style: TextStyle(
          color: Colors.black,
          fontSize: 64,
          fontWeight: FontWeight.w900,
        ),
      ),
    );
  }
}

/// Скелетон
class ShimerTrip extends StatelessWidget {
  const ShimerTrip({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 291,
      right: 60,
      left: 60,
      bottom: 150,
      child: Shimmer.fromColors(
        baseColor: AppColors.secondary,
        highlightColor: AppColors.textSecondary,
        child: ListView.builder(
          itemCount: 3,
          itemBuilder: (context, index) => Padding(
            padding: const EdgeInsets.only(bottom: 18.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    width: 900,
                    height: 178.0,
                    decoration: BoxDecoration(
                      color: AppColors.secondary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
