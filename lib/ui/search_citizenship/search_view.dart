import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/ui/helpers.dart';
import '../../src/globals.dart';
import 'search_logic.dart';

class SearchCitizenshipScreen extends StatelessWidget {
  SearchCitizenshipScreen({Key? key}) : super(key: key);

  final logic = Get.put(SearchCitizenshipLogic());
  final globals = Get.find<Globals>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: 1920,
        height: 1080,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(color: Colors.white),
        child: Stack(
          children: [
            Title(),
            SearchField(logic: logic),

            SearchKeyboard(logic: logic),
            SearchBlock(),

            // GroupHead(),
            Obx(
              () {
                // log(globals.dataState.value.toString()) ;
                if (globals.dataState.value == DataLoadingState.LOADING) {
                  return Positioned(
                      top: 60,
                      right: 37,
                      bottom: 60,
                      left: 1480,
                      child: Center(
                        child: CircularProgressIndicator(),
                      ));
                }
                return Positioned(
                  top: 60,
                  right: 37,
                  bottom: 60,
                  child: Container(
                    width: 376,
                    height: double.maxFinite,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: logic.generateItemsList(logic.countries),
                      ),
                    ),
                  ),
                );
              },
            ),

            SearchTextDivider(),
            InfoBlock(),
            BackButton(globals),
          ],
        ),
      ),
    );
  }
}

class SearchTextDivider extends StatelessWidget {
  const SearchTextDivider({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 411,
      top: 337,
      child: Container(
        width: 646,
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 4,
              strokeAlign: BorderSide.strokeAlignCenter,
              color: Color(0xFFCEE1F4),
            ),
          ),
        ),
      ),
    );
  }
}

class BackButton extends StatelessWidget {
  const BackButton(this.globals);

  final Globals globals;
  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 60,
      top: 80,
      child: Container(
        width: 76,
        height: 76,
        child: Stack(
          children: [
            Positioned(
              left: 0,
              top: 0,
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  Get.back();
                },
                child: Container(
                  width: 76,
                  height: 76,
                  decoration: ShapeDecoration(
                    color: Color(0x191E90FF),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(32),
                    ),
                  ),
                  child: Center(
                    child: Container(
                      width: 36,
                      height: 36,
                      child: Image.asset(
                        "lib/assets/images/ic24-arrow-left.png",
                        color: Color(0xFF1E90FF),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class InfoBlock extends StatelessWidget {
  const InfoBlock({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 443,
      top: 445,
      child: Container(
        width: 639.36,
        height: 90,
        child: Stack(
          children: [
            Positioned(
              left: 0,
              top: 0,
              child: Container(
                width: 639.36,
                height: 90,
                child: Image.asset("lib/assets/images/search_bg.png"),
              ),
            ),
            Center(
              child: Text(
                'Выберите подходящий из списка',
                style: TextStyle(
                  color: Color(0xFF6F8BA6),
                  fontSize: 28,
                  fontFamily: 'Overpass',
                  fontWeight: FontWeight.w900,
                  height: 0,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SearchBlock extends StatelessWidget {
  const SearchBlock({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 1470,
      top: 0,
      child: Container(
        width: 450,
        height: 1080,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(-0.00, 1.00),
            end: Alignment(0, -1),
            colors: [Color(0xFFE6F1FC), Color(0xFFF1F8FF)],
          ),
        ),
      ),
    );
  }
}

class SearchKeyboard extends StatelessWidget {
  const SearchKeyboard({
    super.key,
    required this.logic,
  });

  final SearchCitizenshipLogic logic;

  @override
  Widget build(BuildContext context) {
    return Positioned(
        bottom: 0,
        left: 0,
        child: Keyboard(
          logic.searchTextController,
          numbers: false,
        ));
  }
}

class SearchField extends StatelessWidget {
  const SearchField({
    super.key,
    required this.logic,
  });

  final SearchCitizenshipLogic logic;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 265,
      top: 243,
      child: Container(
        width: 900,
        height: 100,
        child: TextField(
          autofocus: true,
          decoration: InputDecoration.collapsed(
              hintText: "Начните писать...",
              hintStyle: TextStyle(
                color: Colors.grey,
              )),
          onTapOutside: (event) {
            FocusScope.of(context).requestFocus(logic.searchFocusNode);
          },
          onChanged: (val) {},
          showCursor: true,
          focusNode: logic.searchFocusNode,
          controller: logic.searchTextController,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.black,
            fontSize: 64,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}

class Title extends StatelessWidget {
  const Title({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 271,
      top: 80,
      child: Text(
        'Найдите страну гражданства',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.black,
          fontSize: 64,
          fontWeight: FontWeight.w900,
        ),
      ),
    );
  }
}
