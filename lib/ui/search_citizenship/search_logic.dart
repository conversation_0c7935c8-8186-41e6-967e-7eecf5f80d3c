import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rxdart/rxdart.dart';
import 'package:terminal/models/counties_response.dart';
import 'package:terminal/src/globals.dart';
import '../../src/api.dart';

class SearchCitizenshipLogic extends GetxController {
  final api = Get.find<Api>();
  final globals = Get.find<Globals>();

  TextEditingController searchTextController = TextEditingController();
  FocusNode searchFocusNode = FocusNode();
  final searchText = "".obs;
  final countries = <Country>[].obs;

  @override
  onInit() {
    countries.value = globals.countriesList;

    searchTextController.addListener(() {
      searchText.value = searchTextController.text;
    });

    searchText.stream.debounceTime(Duration(milliseconds: 10)).listen((value) async {
      // final res = await api.getDispatchStationsByName(searchTextController.text) ;
      countries.value = globals.countriesList.where((c) => c.title!.toLowerCase().startsWith(searchTextController.text.toLowerCase())).toList();
    });

    super.onInit();
  }

  List<Widget> generateItemsList(List<Country> countries) {
    List<Widget> widgets = [];

    const cityStyle = TextStyle(color: Colors.black, fontSize: 20, fontWeight: FontWeight.w800);

    Widget countryItem(Country country) {
      return InkWell(
        onTap: () {
          log(country.toJson().toString());
          globals.tripOrder.selectedCitizenshipId = country.id ?? 0;
          Get.back();
        },
        child: Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Container(
            width: 376,
            // height: 130,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.85),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0, vertical: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "${country.title}",
                    style: cityStyle,
                    maxLines: null,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    void createCatalog(List<Country> countries) {
      for (var country in countries) {
        widgets.add(countryItem(country));
      }
    }

    if (countries.isEmpty) {
      return [];
    } else {
      createCatalog(countries);
    }

    return widgets;
  }
}
