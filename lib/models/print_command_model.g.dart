// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'print_command_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PrintCommand _$PrintCommandFromJson(Map<String, dynamic> json) => PrintCommand(
      url: json['url'] as String?,
      method: json['method'] as String?,
      command: json['command'] == null
          ? null
          : InnerCommand.fromJson(json['command'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PrintCommandToJson(PrintCommand instance) =>
    <String, dynamic>{
      'url': instance.url,
      'method': instance.method,
      'command': instance.command,
    };

InnerCommand _$InnerCommandFromJson(Map<String, dynamic> json) => InnerCommand(
      command: json['Command'] as String?,
      numDevice: (json['NumDevice'] as num?)?.toInt(),
      idDevice: (json['IdDevice'] as num?)?.toInt(),
      idCommand: json['IdCommand'] as String?,
      checkStrings: (json['CheckStrings'] as List<dynamic>?)
          ?.map((e) => CheckString.fromJson(e as Map<String, dynamic>))
          .toList(),
      notPrint: json['NotPrint'] as bool?,
    );

Map<String, dynamic> _$InnerCommandToJson(InnerCommand instance) =>
    <String, dynamic>{
      'Command': instance.command,
      'NumDevice': instance.numDevice,
      'IdDevice': instance.idDevice,
      'IdCommand': instance.idCommand,
      'CheckStrings': instance.checkStrings,
      'NotPrint': instance.notPrint,
    };

CheckString _$CheckStringFromJson(Map<String, dynamic> json) => CheckString(
      printText: json['PrintText'] == null
          ? null
          : PrintText.fromJson(json['PrintText'] as Map<String, dynamic>),
      barCode: json['BarCode'] == null
          ? null
          : BarCode.fromJson(json['BarCode'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CheckStringToJson(CheckString instance) =>
    <String, dynamic>{
      'PrintText': instance.printText,
      'BarCode': instance.barCode,
    };

PrintText _$PrintTextFromJson(Map<String, dynamic> json) => PrintText(
      text: json['Text'] as String?,
      font: (json['Font'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PrintTextToJson(PrintText instance) => <String, dynamic>{
      'Text': instance.text,
      'Font': instance.font,
    };

BarCode _$BarCodeFromJson(Map<String, dynamic> json) => BarCode(
      barcodeType: json['BarcodeType'] as String?,
      barcode: json['Barcode'] as String?,
    );

Map<String, dynamic> _$BarCodeToJson(BarCode instance) => <String, dynamic>{
      'BarcodeType': instance.barcodeType,
      'Barcode': instance.barcode,
    };
