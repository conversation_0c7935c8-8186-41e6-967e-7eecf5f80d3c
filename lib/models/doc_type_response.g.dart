// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'doc_type_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DocTypesResponse _$DocTypesResponseFromJson(Map<String, dynamic> json) =>
    DocTypesResponse(
      count: (json['count'] as num?)?.toInt(),
      next: json['next'] as String?,
      previous: json['previous'] as String?,
      results: (json['results'] as List<dynamic>?)
          ?.map((e) => DocType.fromJson(e as Map<String, dynamic>))
          .toList(),
      error: json['error'] as String?,
    );

Map<String, dynamic> _$DocTypesResponseToJson(DocTypesResponse instance) =>
    <String, dynamic>{
      'count': instance.count,
      'next': instance.next,
      'previous': instance.previous,
      'results': instance.results,
      'error': instance.error,
    };

DocType _$DocTypeFromJson(Map<String, dynamic> json) => DocType(
      id: (json['id'] as num?)?.toInt(),
      availableRangeAge: json['available_range_age'] == null
          ? null
          : AvailableRangeAge.fromJson(
              json['available_range_age'] as Map<String, dynamic>),
      title: json['title'] as String?,
      isActive: json['is_active'] as bool?,
      useDefault: json['use_default'] as bool?,
      mask: json['mask'] as String?,
      maskDescription: json['mask_description'] as String?,
      maskExample: json['mask_example'] as String?,
    );

Map<String, dynamic> _$DocTypeToJson(DocType instance) => <String, dynamic>{
      'id': instance.id,
      'available_range_age': instance.availableRangeAge,
      'title': instance.title,
      'is_active': instance.isActive,
      'use_default': instance.useDefault,
      'mask': instance.mask,
      'mask_description': instance.maskDescription,
      'mask_example': instance.maskExample,
    };

AvailableRangeAge _$AvailableRangeAgeFromJson(Map<String, dynamic> json) =>
    AvailableRangeAge(
      lower: (json['lower'] as num?)?.toInt(),
      upper: (json['upper'] as num?)?.toInt(),
      bounds: $enumDecodeNullable(_$BoundsEnumMap, json['bounds']),
    );

Map<String, dynamic> _$AvailableRangeAgeToJson(AvailableRangeAge instance) =>
    <String, dynamic>{
      'lower': instance.lower,
      'upper': instance.upper,
      'bounds': _$BoundsEnumMap[instance.bounds],
    };

const _$BoundsEnumMap = {
  Bounds.BOUNDS: '[)',
  Bounds.EMPTY: '()',
  Bounds.PURPLE: '[]',
};
