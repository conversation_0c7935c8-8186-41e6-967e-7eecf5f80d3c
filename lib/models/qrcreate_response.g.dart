// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'qrcreate_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QRCreateResponse _$QRCreateResponseFromJson(Map<String, dynamic> json) =>
    QRCreateResponse(
      uuid: json['uuid'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      expiredAt: DateTime.parse(json['expired_at'] as String),
      status: Status.fromJson(json['status'] as Map<String, dynamic>),
      customer: json['customer'],
      url: json['url'] as String,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$QRCreateResponseToJson(QRCreateResponse instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'created_at': instance.createdAt.toIso8601String(),
      'expired_at': instance.expiredAt.toIso8601String(),
      'status': instance.status,
      'customer': instance.customer,
      'url': instance.url,
      'error': instance.error,
    };

Status _$StatusFromJson(Map<String, dynamic> json) => Status(
      id: json['id'] as String,
      title: json['title'] as String,
    );

Map<String, dynamic> _$StatusToJson(Status instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
    };
