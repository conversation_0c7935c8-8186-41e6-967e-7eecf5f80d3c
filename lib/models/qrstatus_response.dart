import 'dart:convert';
import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:terminal/models/doc_type_response.dart';
import 'package:terminal/models/counties_response.dart';

part 'qrstatus_response.g.dart';

@JsonSerializable()
class QRAuthStatusResponse {
  final String? uuid;
  @JsonKey(name: "created_at")
  final DateTime? createdAt;
  @JsonKey(name: "expired_at")
  final DateTime? expiredAt;
  final Status? status;
  final Customer? customer;
  final String? url;

  QRAuthStatusResponse({
    this.uuid,
    this.createdAt,
    this.expiredAt,
    this.status,
    this.customer,
    this.url,
  });

  factory QRAuthStatusResponse.fromRawJson(String str) => QRAuthStatusResponse.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory QRAuthStatusResponse.fromJson(Map<String, dynamic> json) => _$QRAuthStatusResponseFromJson(json);
  Map<String, dynamic> toJson() => _$QRAuthStatusResponseToJson(this);
}

@JsonSerializable()
class Customer {
  final int? id;
  final String? email;
  final String? phone;
  final List<Passenger>? passengers;

  Customer({
    this.id,
    this.email,
    this.phone,
    this.passengers,
  });

  factory Customer.fromRawJson(String str) => Customer.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory Customer.fromJson(Map<String, dynamic> json) => _$CustomerFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerToJson(this);
}

@JsonSerializable()
class Passenger {
  int? id;
  @JsonKey(name: "last_name")
  String? lastName;
  @JsonKey(name: "first_name")
  String? firstName;
  @JsonKey(name: "second_name")
  String? secondName;
  int? sex;
  @JsonKey(name: "date_of_birthday")
  String? dateOfBirthday;
  Country? citizenship;
  Country? country;
  @JsonKey(name: "type_document")
  DocType? typeDocument;
  @JsonKey(name: "seria_number")
  String? seriaNumber;
  int? customer;
  @JsonKey(name: "is_active")
  bool? isActive;

  String? error;

  final baggage = 0.obs;
  final isSelected = false.obs;

  Passenger({
    this.id,
    this.lastName,
    this.firstName,
    this.secondName,
    this.sex,
    this.dateOfBirthday,
    this.citizenship,
    this.country,
    this.typeDocument,
    this.seriaNumber,
    this.customer,
    this.isActive,
  });

  bool? get isChild {
    if (dateOfBirthday != null) return getAgeFrom(dateOfBirthday) < 14;
    return null;
  }

  bool get isEmpty {
    return this.firstName == null || this.secondName == null;
  }

  String fioString() {
    if (this.secondName == null) return "${this.lastName}";
    return "${this.lastName} ${this.firstName?.substring(0, 1).toUpperCase()}. ${this.secondName?.substring(0, 1).toUpperCase()}.";
  }

  String docString() {
    if (this.typeDocument == null || this.typeDocument?.title == null) return "";
    return "${this.typeDocument?.title?.split(' ')[0]}";
  }

  String seriaString() {
    if (this.seriaNumber == null || this.seriaNumber == "") return "";

    return "${this.seriaNumber?.padRight(6, ' ').substring(0, 2)} *** ${this.seriaNumber?.padRight(6, ' ').substring(6)}";
  }

  int getAgeFrom(String? bdString) {
    DateTime? bd = DateTime.tryParse('${dateOfBirthday?.split('.')[2]}-${dateOfBirthday?.split('.')[1]}-${dateOfBirthday?.split('.')[0]}');

    if (bd == null) {
      return 0;
    } else {
      if ((DateTime.now().month < bd.month) || ((DateTime.now().month == bd.month) && (DateTime.now().day < bd.day))) {
        return (DateTime.now().year - bd.year - 1);
      } else {
        return (DateTime.now().year - bd.year);
      }
    }
  }

  factory Passenger.fromRawJson(String str) => Passenger.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Passenger.fromJson(Map<String, dynamic> json) => _$PassengerFromJson(json);
  Map<String, dynamic> toJson() => _$PassengerToJson(this);
}

@JsonSerializable()
class Status {
  final String? id;
  final String? title;

  Status({
    this.id,
    this.title,
  });

  factory Status.fromRawJson(String str) => Status.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory Status.fromJson(Map<String, dynamic> json) => _$StatusFromJson(json);
  Map<String, dynamic> toJson() => _$StatusToJson(this);
}
