// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trip_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Trip _$TripFromJson(Map<String, dynamic> json) => Trip(
      pointA: json['point_a'] == null
          ? null
          : StationPoint.fromJson(json['point_a'] as Map<String, dynamic>),
      pointB: json['point_b'] == null
          ? null
          : StationPoint.fromJson(json['point_b'] as Map<String, dynamic>),
      start: json['start'] == null
          ? null
          : DateTime.parse(json['start'] as String),
      tripId: json['trip_id'] as String?,
      route: json['route'] as String?,
      concreate: json['concreate'] as bool?,
      end: json['end'] == null ? null : DateTime.parse(json['end'] as String),
      distance: json['distance'],
      freePlace: (json['free_place'] as num?)?.toInt(),
      freePlaces: (json['free_places'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      priceFull: (json['price_full'] as num?)?.toDouble(),
      priceChild: (json['price_child'] as num?)?.toDouble(),
      priceBaggage: (json['price_baggage'] as num?)?.toDouble(),
      priceInsurance: (json['price_insurance'] as num?)?.toDouble(),
      priceDues: (json['price_dues'] as num?)?.toDouble(),
      busInfo: json['bus_info'] as String?,
      number: json['number'] as String?,
      travelTime: (json['travel_time'] as num?)?.toInt(),
      travelTimeHuman: json['travel_time_human'] as String?,
      carrier: json['carrier'] as String?,
      schedule: json['schedule'] as String?,
      platform: json['platform'] as String?,
      withoutPd: json['without_pd'] as bool?,
      withoutSelectPlaces: json['without_select_places'] as bool?,
      status: json['status'],
      saleStatus: json['sale_status'],
      availableForSale: json['available_for_sale'] as bool?,
      commission: (json['commission'] as num?)?.toInt(),
      childTariffName: json['child_tariff_name'],
      childAgeFrom: json['child_age_from'],
      childAgeTo: json['child_age_to'],
      noticeInfo: json['notice_info'] == null
          ? null
          : NoticeInfo.fromJson(json['notice_info'] as Map<String, dynamic>),
      insuranceInfo: json['insurance_info'] == null
          ? null
          : InsuranceInfo.fromJson(
              json['insurance_info'] as Map<String, dynamic>),
      commissionInfo: json['commission_info'] == null
          ? null
          : CommissionInfo.fromJson(
              json['commission_info'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TripToJson(Trip instance) => <String, dynamic>{
      'point_a': instance.pointA,
      'point_b': instance.pointB,
      'start': instance.start?.toIso8601String(),
      'trip_id': instance.tripId,
      'route': instance.route,
      'concreate': instance.concreate,
      'end': instance.end?.toIso8601String(),
      'distance': instance.distance,
      'free_place': instance.freePlace,
      'free_places': instance.freePlaces,
      'price_full': instance.priceFull,
      'price_child': instance.priceChild,
      'price_baggage': instance.priceBaggage,
      'price_insurance': instance.priceInsurance,
      'price_dues': instance.priceDues,
      'bus_info': instance.busInfo,
      'number': instance.number,
      'travel_time': instance.travelTime,
      'travel_time_human': instance.travelTimeHuman,
      'carrier': instance.carrier,
      'schedule': instance.schedule,
      'platform': instance.platform,
      'without_pd': instance.withoutPd,
      'without_select_places': instance.withoutSelectPlaces,
      'status': instance.status,
      'sale_status': instance.saleStatus,
      'available_for_sale': instance.availableForSale,
      'commission': instance.commission,
      'child_tariff_name': instance.childTariffName,
      'child_age_from': instance.childAgeFrom,
      'child_age_to': instance.childAgeTo,
      'notice_info': instance.noticeInfo,
      'insurance_info': instance.insuranceInfo,
      'commission_info': instance.commissionInfo,
    };

CommissionInfo _$CommissionInfoFromJson(Map<String, dynamic> json) =>
    CommissionInfo(
      adult: (json['adult'] as num?)?.toDouble(),
      child: (json['child'] as num?)?.toDouble(),
      baggage: (json['baggage'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$CommissionInfoToJson(CommissionInfo instance) =>
    <String, dynamic>{
      'adult': instance.adult,
      'child': instance.child,
      'baggage': instance.baggage,
    };

InsuranceInfo _$InsuranceInfoFromJson(Map<String, dynamic> json) =>
    InsuranceInfo(
      insuranceAvailable: json['insurance_available'] as bool?,
      useInsuranceDefault: json['use_insurance_default'] as bool?,
      priceInsurerAdult: (json['price_insurer_adult'] as num?)?.toDouble(),
      priceInsurerChild: (json['price_insurer_child'] as num?)?.toDouble(),
      insurerRules: json['insurer_rules'] as String?,
    );

Map<String, dynamic> _$InsuranceInfoToJson(InsuranceInfo instance) =>
    <String, dynamic>{
      'insurance_available': instance.insuranceAvailable,
      'use_insurance_default': instance.useInsuranceDefault,
      'price_insurer_adult': instance.priceInsurerAdult,
      'price_insurer_child': instance.priceInsurerChild,
      'insurer_rules': instance.insurerRules,
    };

NoticeInfo _$NoticeInfoFromJson(Map<String, dynamic> json) => NoticeInfo(
      noticeAvailable: json['notice_available'] as bool?,
      useNoticeDefault: json['use_notice_default'] as bool?,
      priceNotice: (json['price_notice'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$NoticeInfoToJson(NoticeInfo instance) =>
    <String, dynamic>{
      'notice_available': instance.noticeAvailable,
      'use_notice_default': instance.useNoticeDefault,
      'price_notice': instance.priceNotice,
    };

StationPoint _$StationPointFromJson(Map<String, dynamic> json) => StationPoint(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      timezone: json['timezone'] as String?,
    );

Map<String, dynamic> _$StationPointToJson(StationPoint instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'timezone': instance.timezone,
    };
