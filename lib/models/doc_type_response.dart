import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'doc_type_response.g.dart';

@JsonSerializable()
class DocTypesResponse {
  final int? count;
  final String? next;
  final String? previous;
  final List<DocType>? results;
  String? error;

  DocTypesResponse({
    this.count,
    this.next,
    this.previous,
    this.results,
    this.error,
  });

  factory DocTypesResponse.fromRawJson(String str) =>
      DocTypesResponse.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory DocTypesResponse.fromJson(Map<String, dynamic> json) =>
      _$DocTypesResponseFromJson(json);
  Map<String, dynamic> toJson() => _$DocTypesResponseToJson(this);
}

@JsonSerializable()
class DocType {
  final int? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: "available_range_age")
  final AvailableRangeAge? availableRangeAge;
  final String? title;
  @Json<PERSON>ey(name: "is_active")
  final bool? isActive;
  @Json<PERSON>ey(name: "use_default")
  final bool? useDefault;
  final String? mask;
  @JsonKey(name: "mask_description")
  final String? maskDescription;
  @JsonKey(name: "mask_example")
  final String? maskExample;

  DocType({
    this.id,
    this.availableRangeAge,
    this.title,
    this.isActive,
    this.useDefault,
    this.mask,
    this.maskDescription,
    this.maskExample,
  });

  factory DocType.fromJson(Map<String, dynamic> json) =>
      _$DocTypeFromJson(json);
  Map<String, dynamic> toJson() => _$DocTypeToJson(this);
}

@JsonSerializable()
class AvailableRangeAge {
  final int? lower;
  final int? upper;
  final Bounds? bounds;

  AvailableRangeAge({
    this.lower,
    this.upper,
    this.bounds,
  });

  factory AvailableRangeAge.fromJson(Map<String, dynamic> json) =>
      _$AvailableRangeAgeFromJson(json);
  Map<String, dynamic> toJson() => _$AvailableRangeAgeToJson(this);
}

@JsonEnum(fieldRename: FieldRename.none)
enum Bounds {
  @JsonValue("[)")
  BOUNDS,

  @JsonValue("()")
  EMPTY,

  @JsonValue("[]")
  PURPLE,
}

final boundsValues =
    EnumValues({"[)": Bounds.BOUNDS, "()": Bounds.EMPTY, "[]": Bounds.PURPLE});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
