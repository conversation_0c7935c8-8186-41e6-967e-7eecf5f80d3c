// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateOrderModel _$CreateOrderModelFromJson(Map<String, dynamic> json) =>
    CreateOrderModel(
      id: (json['id'] as num?)?.toInt(),
      uuid: json['uuid'] as String?,
      date: json['date'] as String?,
      buyerEmail: json['buyer_email'] as String?,
      buyerPhone: json['buyer_phone'] as String?,
      status: json['status'] == null
          ? null
          : Status.fromJson(json['status'] as Map<String, dynamic>),
      sumAll: (json['summ_all'] as num?)?.toInt(),
      payment: json['payment'] == null
          ? null
          : Payment.fromJson(json['payment'] as Map<String, dynamic>),
      paymentReturn: json['payment_return'] == null
          ? null
          : Payment.fromJson(json['payment_return'] as Map<String, dynamic>),
      trips: (json['trips'] as List<dynamic>?)
          ?.map((e) => TripPayElement.fromJson(e as Map<String, dynamic>))
          .toList(),
      orderType: json['order_type'] == null
          ? null
          : OrderType.fromJson(json['order_type'] as Map<String, dynamic>),
      url: json['url'] as String?,
      error: json['error'] as String?,
      printCommands: (json['print_commands'] as List<dynamic>?)
          ?.map((e) => PrintCommand.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CreateOrderModelToJson(CreateOrderModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uuid': instance.uuid,
      'date': instance.date,
      'buyer_email': instance.buyerEmail,
      'buyer_phone': instance.buyerPhone,
      'status': instance.status,
      'summ_all': instance.sumAll,
      'payment': instance.payment,
      'payment_return': instance.paymentReturn,
      'trips': instance.trips,
      'order_type': instance.orderType,
      'url': instance.url,
      'error': instance.error,
      'print_commands': instance.printCommands,
    };

Payment _$PaymentFromJson(Map<String, dynamic> json) => Payment(
      id: (json['id'] as num?)?.toInt(),
      status: json['status'] == null
          ? null
          : Status.fromJson(json['status'] as Map<String, dynamic>),
      errors: json['errors'] as String?,
      paidAt: json['paid_at'] as String?,
      response: json['response'] == null
          ? null
          : ResponsePayment.fromJson(json['response'] as Map<String, dynamic>),
      paymentType: json['payment_type'] == null
          ? null
          : PaymentType.fromJson(json['payment_type'] as Map<String, dynamic>),
      setting: json['setting'] as String?,
      data: Payment._parsePaymentData(json['data']),
    );

Map<String, dynamic> _$PaymentToJson(Payment instance) => <String, dynamic>{
      'id': instance.id,
      'status': instance.status,
      'errors': instance.errors,
      'paid_at': instance.paidAt,
      'response': instance.response,
      'payment_type': instance.paymentType,
      'setting': instance.setting,
      'data': Payment._writePaymentData(instance.data),
    };

ResponsePayment _$ResponsePaymentFromJson(Map<String, dynamic> json) =>
    ResponsePayment(
      id: json['id'] as String?,
      amount: json['amount'] == null
          ? null
          : Amount.fromJson(json['amount'] as Map<String, dynamic>),
      status: json['status'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      paymentId: json['payment_id'] as String?,
      done: json['done'] as bool?,
    );

Map<String, dynamic> _$ResponsePaymentToJson(ResponsePayment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'amount': instance.amount,
      'status': instance.status,
      'created_at': instance.createdAt?.toIso8601String(),
      'payment_id': instance.paymentId,
      'done': instance.done,
    };

Amount _$AmountFromJson(Map<String, dynamic> json) => Amount(
      value: Amount._stringToDouble(json['value']),
      currency: json['currency'] as String?,
    );

Map<String, dynamic> _$AmountToJson(Amount instance) => <String, dynamic>{
      'value': Amount._doubleToString(instance.value),
      'currency': instance.currency,
    };

PaymentType _$PaymentTypeFromJson(Map<String, dynamic> json) => PaymentType(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
    );

Map<String, dynamic> _$PaymentTypeToJson(PaymentType instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
    };

OrderType _$OrderTypeFromJson(Map<String, dynamic> json) => OrderType(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
    );

Map<String, dynamic> _$OrderTypeToJson(OrderType instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
    };

Status _$StatusFromJson(Map<String, dynamic> json) => Status(
      id: Status._idFromJson(json['id']),
      title: json['title'] as String,
    );

Map<String, dynamic> _$StatusToJson(Status instance) => <String, dynamic>{
      'id': Status._idToJson(instance.id),
      'title': instance.title,
    };

TripPayElement _$TripPayElementFromJson(Map<String, dynamic> json) =>
    TripPayElement(
      id: (json['id'] as num?)?.toInt(),
      count: (json['count'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toInt(),
      sumAll: (json['sum_all'] as num?)?.toInt(),
      status: json['status'] == null
          ? null
          : Status.fromJson(json['status'] as Map<String, dynamic>),
      trip: json['trip'] == null
          ? null
          : TripPay.fromJson(json['trip'] as Map<String, dynamic>),
      tickets: (json['tickets'] as List<dynamic>?)
          ?.map((e) => Ticket.fromJson(e as Map<String, dynamic>))
          .toList(),
      errors: json['errors'],
      checksOnline: json['checks_online'] as List<dynamic>?,
    );

Map<String, dynamic> _$TripPayElementToJson(TripPayElement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'count': instance.count,
      'price': instance.price,
      'sum_all': instance.sumAll,
      'status': instance.status,
      'trip': instance.trip,
      'tickets': instance.tickets,
      'errors': instance.errors,
      'checks_online': instance.checksOnline,
    };

TripPay _$TripPayFromJson(Map<String, dynamic> json) => TripPay(
      id: (json['id'] as num?)?.toInt(),
      externalId: json['external_id'] as String?,
      pointA: json['point_a'] == null
          ? null
          : Point.fromJson(json['point_a'] as Map<String, dynamic>),
      pointB: json['point_b'] == null
          ? null
          : Point.fromJson(json['point_b'] as Map<String, dynamic>),
      start: json['start'] as String?,
      end: json['end'] as String?,
      tripId: json['trip_id'] as String?,
      route: json['route'] as String?,
      carrier: json['carrier'] as String?,
      platform: json['platform'] as String?,
      travelTime: (json['travel_time'] as num?)?.toInt(),
      travelTimeHuman: json['travel_time_human'] as String?,
      title: json['title'] as String?,
    );

Map<String, dynamic> _$TripPayToJson(TripPay instance) => <String, dynamic>{
      'id': instance.id,
      'external_id': instance.externalId,
      'point_a': instance.pointA,
      'point_b': instance.pointB,
      'start': instance.start,
      'end': instance.end,
      'trip_id': instance.tripId,
      'route': instance.route,
      'carrier': instance.carrier,
      'platform': instance.platform,
      'travel_time': instance.travelTime,
      'travel_time_human': instance.travelTimeHuman,
      'title': instance.title,
    };

Point _$PointFromJson(Map<String, dynamic> json) => Point(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      timezone: json['timezone'] as String?,
    );

Map<String, dynamic> _$PointToJson(Point instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'timezone': instance.timezone,
    };

Ticket _$TicketFromJson(Map<String, dynamic> json) => Ticket(
      id: (json['id'] as num?)?.toInt(),
      status: json['status'] == null
          ? null
          : Status.fromJson(json['status'] as Map<String, dynamic>),
      url: json['url'] as String?,
      returnTicket: json['return_ticket'] == null
          ? null
          : ReturnTicket.fromJson(
              json['return_ticket'] as Map<String, dynamic>),
      externalId: json['external_id'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      title: json['title'] as String?,
      isActive: json['is_active'] as bool?,
      date: json['date'] as String?,
      dateReserve: json['date_reserve'] as String?,
      ticketNumber: json['ticket_number'] as String?,
      placeNumber: json['place_number'] as String?,
      isStand: json['is_stand'] as bool?,
      isChild: json['is_child'] as bool?,
      isBaggage: json['is_baggage'] as bool?,
      barcode: json['barcode'],
      isReturn: json['is_return'] as bool?,
      commissionValue: (json['commission_value'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toInt(),
      priceBase: (json['price_base'] as num?)?.toInt(),
      priceBaggage: (json['price_baggage'] as num?)?.toInt(),
      priceBaggageBase: (json['price_baggage_base'] as num?)?.toInt(),
      priceDues: (json['price_dues'] as num?)?.toInt(),
      sumBaggage: (json['sum_baggage'] as num?)?.toInt(),
      sumBaggageBase: (json['sum_baggage_base'] as num?)?.toInt(),
      countBaggage: (json['count_baggage'] as num?)?.toInt(),
      externalIdBaggage: json['external_id_baggage'] as List<dynamic>?,
      sumAll: (json['sum_all'] as num?)?.toInt(),
      sumAllBase: (json['sum_all_base'] as num?)?.toInt(),
      sumInsurance: (json['sum_insurance'] as num?)?.toInt(),
      uuid: json['uuid'] as String?,
      isExternal: json['is_external'] as bool?,
      externalData: json['external_data'] as Map<String, dynamic>?,
      company: (json['company'] as num?)?.toInt(),
      passTicket: json['pass_ticket'],
      passenger: const PassengerConverter().fromJson(json['passenger']),
      customer: (json['customer'] as num?)?.toInt(),
      trip: json['trip'] == null
          ? null
          : Trip.fromJson(json['trip'] as Map<String, dynamic>),
    )..paymentButton = json['payment_button'] == null
        ? null
        : PaymentButton.fromJson(
            json['payment_button'] as Map<String, dynamic>);

Map<String, dynamic> _$TicketToJson(Ticket instance) => <String, dynamic>{
      'id': instance.id,
      'status': instance.status,
      'url': instance.url,
      'return_ticket': instance.returnTicket,
      'external_id': instance.externalId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'title': instance.title,
      'is_active': instance.isActive,
      'date': instance.date,
      'date_reserve': instance.dateReserve,
      'ticket_number': instance.ticketNumber,
      'place_number': instance.placeNumber,
      'is_stand': instance.isStand,
      'is_child': instance.isChild,
      'is_baggage': instance.isBaggage,
      'barcode': instance.barcode,
      'is_return': instance.isReturn,
      'commission_value': instance.commissionValue,
      'price': instance.price,
      'price_base': instance.priceBase,
      'price_baggage': instance.priceBaggage,
      'price_baggage_base': instance.priceBaggageBase,
      'price_dues': instance.priceDues,
      'sum_baggage': instance.sumBaggage,
      'sum_baggage_base': instance.sumBaggageBase,
      'count_baggage': instance.countBaggage,
      'external_id_baggage': instance.externalIdBaggage,
      'sum_all': instance.sumAll,
      'sum_all_base': instance.sumAllBase,
      'sum_insurance': instance.sumInsurance,
      'uuid': instance.uuid,
      'is_external': instance.isExternal,
      'external_data': instance.externalData,
      'company': instance.company,
      'pass_ticket': instance.passTicket,
      'passenger': const PassengerConverter().toJson(instance.passenger),
      'customer': instance.customer,
      'trip': instance.trip,
      'payment_button': instance.paymentButton,
    };

ReturnTicket _$ReturnTicketFromJson(Map<String, dynamic> json) => ReturnTicket(
      status: json['status'] == null
          ? null
          : Status.fromJson(json['status'] as Map<String, dynamic>),
      date: json['date'] as String?,
      returnType: json['return_type'] as String?,
      sumReturn: (json['sum_return'] as num?)?.toDouble(),
      errors: json['errors'] as String?,
      order: (json['order'] as num?)?.toDouble(),
      orderId: (json['order_id'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$ReturnTicketToJson(ReturnTicket instance) =>
    <String, dynamic>{
      'status': instance.status,
      'date': instance.date,
      'return_type': instance.returnType,
      'sum_return': instance.sumReturn,
      'errors': instance.errors,
      'order': instance.order,
      'order_id': instance.orderId,
    };
