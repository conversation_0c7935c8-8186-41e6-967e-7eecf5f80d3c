import 'package:get/get.dart';
import 'package:terminal/models/create_order_model.dart';
import 'package:terminal/models/qrstatus_response.dart';
import 'package:terminal/models/trip_detail_model.dart';
import 'package:terminal/models/trip_model.dart';
import 'package:timezone/timezone.dart' as tz;

import 'order_to_place.dart';

enum SeatType { CHILD, ADULT }

class Seat {
  int seatNumber = 0;
  SeatType type;

  Seat({
    required this.seatNumber,
    required this.type,
  });
}

class TripOrder {
  final selectedTrip = Trip().obs; // откуда - куда - когда
  final selectedTripDetail = TripDetail().obs; // цены и детали поездки
  final orderToPlace = OrderToPlace().obs;
  final selectedSeats = List<Seat>.empty(growable: true).obs; // выбранные места
  final lkPassengersList = List<Passenger>.empty(growable: true).obs;
  Customer? customer;
  List<Passenger> passengersList = []; // данные пассажиров
  final baggageCount = 0.obs;
  final adultsCount = 0.obs;
  final childrenCount = 0.obs;
  final noticeInfoMessage = false.obs;
  final insuranceIncluded = false.obs;
  Rx<CreateOrderModel> createOrder = CreateOrderModel().obs;
  final isPaymentProcessingFinished = false.obs; // Определяет конец оплаты и показываем ссыку на билеты

  int selectedCitizenshipId = 197;
  final selectedDocumentTypeId = 101.obs;

  int get totalSelectedSeats => adultsCount.value + childrenCount.value;
  int get lkSelectedChildrenCount => lkPassengersList.where((p) => ((p.isChild ?? false) && p.isSelected.value)).length;
  int get lkSelectedAdultsCount => lkPassengersList.where((p) => (!(p.isChild ?? false) && p.isSelected.value)).length;
  int get selectedBaggageCount => passengersList.fold(0, (previousValue, element) => previousValue + element.baggage.value).toInt();

  /// Сервисный сбор автовокзала
  double get serviceFee {
    return selectedSeats.length * (selectedTripDetail.value.priceDues ?? 0);
  }

  /// Страхование
  double get insuranceFee {
    return (insuranceIncluded.value ? (selectedTripDetail.value.insuranceInfo?.priceInsurerAdult ?? 0) * adultsCount.value : 0) +
        (insuranceIncluded.value ? (selectedTripDetail.value.insuranceInfo?.priceInsurerChild ?? 0) * childrenCount.value : 0);
  }

  /// Сбор за информацию об отмене
  double get noticeFee {
    return noticeInfoMessage.value ? (selectedTripDetail.value.noticeInfo?.priceNotice ?? 0) : 0;
  }

  /// Комиссия за пассажирский билет
  double get commissionTicket {
    final double commissionBaggage = baggageCount.value * (selectedTripDetail.value.commissionInfo?.baggage ?? 0);
    final double commissionAdults = adultsCount.value * (selectedTripDetail.value.commissionInfo?.adult ?? 0);
    final double commissionChald = childrenCount.value * (selectedTripDetail.value.commissionInfo?.child ?? 0);
    return commissionBaggage + commissionAdults + commissionChald;
  }

  double get totalPrice {
    double total = 0;
    total += ((selectedTripDetail.value.priceFull ?? 0) * selectedSeats.where((s) => s.type == SeatType.ADULT).length);
    total += ((selectedTripDetail.value.priceChild ?? 0) * selectedSeats.where((s) => s.type == SeatType.CHILD).length);
    total += ((selectedTripDetail.value.priceBaggage ?? 0) * baggageCount.value);
    total += noticeFee;
    total += insuranceFee;
    total += serviceFee;
    total += commissionTicket;
    return total;
  }

  String get startTimeString => _localTimeString(selectedTripDetail.value.pointA, selectedTripDetail.value.start);
  String get endTimeString => _localTimeString(selectedTripDetail.value.pointB, selectedTripDetail.value.end);

  String _localTimeString(StationPoint? point, DateTime? date) {
    if (date == null || point == null) return "";

    final timezone = point.timezone ?? "Europe/Moscow";
    final location = tz.getLocation(timezone);

    DateTime res = tz.TZDateTime.from(date, location);
    return "${res.hour.toString().padLeft(2, '0')}:${res.minute.toString().padLeft(2, '0')}";
  }
}
