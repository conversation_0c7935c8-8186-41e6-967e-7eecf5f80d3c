import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
import 'package:terminal/models/create_order_model.dart';

part 'create_payment_model.g.dart';

@JsonSerializable()
class CreatePaymentModel {
  final int? id;
  final Status? status;
  final String? errors;
  @JsonKey(name: "redirect_url")
  final String? redirectUrl;
  @JsonKey(fromJson: _parsePaymentData, toJson: _writePaymentData)
  final PaymentData? data;

  CreatePaymentModel({
    this.id,
    this.status,
    this.errors,
    this.redirectUrl,
    this.data,
  });

  bool get isSuccess {
    return status?.id == "done";
  }

  factory CreatePaymentModel.fromJson(Map<String, dynamic> json) => _$CreatePaymentModelFromJson(json);
  Map<String, dynamic> toJson() => _$CreatePaymentModelToJson(this);

  factory CreatePaymentModel.fromRawJson(String str) => CreatePaymentModel.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  /// Кастомный парсер, чтобы игнорировать строки в `data`
  static PaymentData? _parsePaymentData(dynamic json) {
    if (json is Map<String, dynamic>) {
      return PaymentData.fromJson(json);
    }
    return null;
  }

  /// Обычный сериализатор
  static dynamic _writePaymentData(PaymentData? data) => data?.toJson();
}

@JsonSerializable()
class PaymentData {
  final String? url;
  final String? method;
  final PaymentCommand? command;

  PaymentData({this.url, this.method, this.command});

  factory PaymentData.fromJson(Map<String, dynamic> json) => _$PaymentDataFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentDataToJson(this);
}

@JsonSerializable()
class PaymentCommand {
  @JsonKey(name: "Amount", includeIfNull: false)
  final double? amount;
  @JsonKey(name: "Command", includeIfNull: false)
  final String? command;
  @JsonKey(name: "IdDevice", includeIfNull: false)
  final int? idDevice;
  @JsonKey(name: "IdCommand", includeIfNull: false)
  final String? idCommand;
  @JsonKey(name: "NumDevice", includeIfNull: false)
  final int? numDevice;
  @JsonKey(name: "UniversalID", includeIfNull: false)
  final String? universalID;

  PaymentCommand({this.amount, this.command, this.idDevice, this.idCommand, this.numDevice, this.universalID});

  factory PaymentCommand.fromJson(Map<String, dynamic> json) => _$PaymentCommandFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentCommandToJson(this);
}
