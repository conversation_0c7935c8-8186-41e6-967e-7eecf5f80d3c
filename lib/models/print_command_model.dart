import 'package:json_annotation/json_annotation.dart';

part 'print_command_model.g.dart';

@JsonSerializable()
class PrintCommand {
  final String? url;
  final String? method;
  final InnerCommand? command;

  PrintCommand({
    this.url,
    this.method,
    this.command,
  });

  factory PrintCommand.fromJson(Map<String, dynamic> json) => _$PrintCommandFromJson(json);
  Map<String, dynamic> toJson() => _$PrintCommandToJson(this);
}

@JsonSerializable()
class InnerCommand {
  @Json<PERSON>ey(name: "Command")
  final String? command;

  @J<PERSON><PERSON><PERSON>(name: "NumDevice")
  final int? numDevice;

  @<PERSON><PERSON><PERSON><PERSON>(name: "IdDevice")
  final int? idDevice;

  @<PERSON><PERSON><PERSON><PERSON>(name: "IdCommand")
  final String? idCommand;

  @<PERSON><PERSON><PERSON><PERSON>(name: "CheckStrings")
  final List<CheckString>? checkStrings;

  @<PERSON><PERSON><PERSON><PERSON>(name: "NotPrint")
  final bool? notPrint;

  InnerCommand({
    this.command,
    this.numDevice,
    this.idDevice,
    this.idCommand,
    this.checkStrings,
    this.notPrint,
  });

  factory InnerCommand.fromJson(Map<String, dynamic> json) => _$InnerCommandFromJson(json);
  Map<String, dynamic> toJson() => _$InnerCommandToJson(this);
}

@JsonSerializable()
class CheckString {
  @JsonKey(name: "PrintText")
  final PrintText? printText;

  @JsonKey(name: "BarCode")
  final BarCode? barCode;

  CheckString({
    this.printText,
    this.barCode,
  });

  factory CheckString.fromJson(Map<String, dynamic> json) => _$CheckStringFromJson(json);
  Map<String, dynamic> toJson() => _$CheckStringToJson(this);
}

@JsonSerializable()
class PrintText {
  @JsonKey(name: "Text")
  final String? text;

  @JsonKey(name: "Font")
  final int? font;

  PrintText({
    this.text,
    this.font,
  });

  factory PrintText.fromJson(Map<String, dynamic> json) => _$PrintTextFromJson(json);
  Map<String, dynamic> toJson() => _$PrintTextToJson(this);
}

@JsonSerializable()
class BarCode {
  @JsonKey(name: "BarcodeType")
  final String? barcodeType;

  @JsonKey(name: "Barcode")
  final String? barcode;

  BarCode({
    this.barcodeType,
    this.barcode,
  });

  factory BarCode.fromJson(Map<String, dynamic> json) => _$BarCodeFromJson(json);
  Map<String, dynamic> toJson() => _$BarCodeToJson(this);
}
