import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'counties_response.g.dart';

@JsonSerializable()
class CountriesResponse {
  final int? count;
  final String? next;
  final String? previous;
  final List<Country>? results;
  final String? error;

  CountriesResponse({
    this.count,
    this.next,
    this.previous,
    this.results,
    this.error,
  });

  factory CountriesResponse.fromRawJson(String str) =>
      CountriesResponse.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory CountriesResponse.fromJson(Map<String, dynamic> json) =>
      _$CountriesResponseFromJson(json);
  Map<String, dynamic> toJson() => _$CountriesResponseToJson(this);
}

@JsonSerializable()
class Country {
  final int? id;
  final String? title;
  @JsonKey(name: "use_default")
  final bool? useDefault;

  Country({
    this.id,
    this.title,
    this.useDefault,
  });

  factory Country.fromJson(Map<String, dynamic> json) =>
      _$CountryFrom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$CountryToJson(this);
}
