import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'qrcreate_response.g.dart';

@JsonSerializable()
class QRCreateResponse {
  final String uuid;
  @JsonKey(name: "created_at")
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON>ey(name: "expired_at")
  final DateTime expiredAt;
  final Status status;
  final dynamic customer;
  final String url;
  String? error;

  QRCreateResponse({
    required this.uuid,
    required this.createdAt,
    required this.expiredAt,
    required this.status,
    required this.customer,
    required this.url,
    this.error,
  });

  factory QRCreateResponse.fromRawJson(String str) =>
      QRCreateResponse.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory QRCreateResponse.fromJson(Map<String, dynamic> json) =>
      _$QRCreateResponseFromJson(json);
  Map<String, dynamic> toJson() => _$QRCreateResponseToJson(this);
}

@JsonSerializable()
class Status {
  final String id;
  final String title;

  Status({
    required this.id,
    required this.title,
  });

  factory Status.fromJson(Map<String, dynamic> json) => _$StatusFromJson(json);
  Map<String, dynamic> toJson() => _$StatusToJson(this);
}
