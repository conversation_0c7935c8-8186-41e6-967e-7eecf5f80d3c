import 'dart:convert';
import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:terminal/models/create_payment_model.dart';
import 'package:terminal/models/print_command_model.dart';
import 'package:terminal/models/qrstatus_response.dart';
import 'package:terminal/models/trip_detail_model.dart';
import 'package:terminal/models/trip_model.dart';

part 'create_order_model.g.dart';

@JsonSerializable()
class CreateOrderModel {
  final int? id;
  final String? uuid;
  final String? date;
  @JsonKey(name: "buyer_email")
  final String? buyerEmail;
  @JsonKey(name: "buyer_phone")
  final String? buyerPhone;
  final Status? status;
  @Json<PERSON>ey(name: "summ_all")
  final int? sumAll;
  final Payment? payment;
  @JsonKey(name: "payment_return")
  final Payment? paymentReturn;
  List<TripPayElement>? trips;
  @JsonKey(name: "order_type")
  final OrderType? orderType;
  final String? url;
  final String? error;
  @<PERSON><PERSON><PERSON><PERSON>(name: "print_commands")
  List<PrintCommand>? printCommands;

  bool get isSuccessPaymentOrder => status?.id == "2";
  bool get isDontPaymentOrder => status?.id == "3"; //Не оплачен
  bool get isErrorPaymentOrder => status?.id == "4";

  CreateOrderModel({
    this.id,
    this.uuid,
    this.date,
    this.buyerEmail,
    this.buyerPhone,
    this.status,
    this.sumAll,
    this.payment,
    this.paymentReturn,
    this.trips,
    this.orderType,
    this.url,
    this.error,
    this.printCommands,
  });

  factory CreateOrderModel.fromJson(Map<String, dynamic> json) => _$CreateOrderModelFromJson(json);
  Map<String, dynamic> toJson() => _$CreateOrderModelToJson(this);

  factory CreateOrderModel.fromRawJson(String str) => CreateOrderModel.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());
}

//MARK: - Payment
@JsonSerializable()
class Payment {
  final int? id;
  final Status? status;
  final String? errors;
  @JsonKey(name: "paid_at")
  final String? paidAt;
  final ResponsePayment? response;
  @JsonKey(name: "payment_type")
  final PaymentType? paymentType;
  final String? setting;
  @JsonKey(fromJson: _parsePaymentData, toJson: _writePaymentData)
  final PaymentData? data;

  Payment({
    this.id,
    this.status,
    this.errors,
    this.paidAt,
    this.response,
    this.paymentType,
    this.setting,
    this.data,
  });

  factory Payment.fromJson(Map<String, dynamic> json) => _$PaymentFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentToJson(this);

  /// Кастомный парсер, чтобы игнорировать строки в `data`
  static PaymentData? _parsePaymentData(dynamic json) {
    if (json is Map<String, dynamic>) {
      return PaymentData.fromJson(json);
    }
    return null;
  }

  /// Обычный сериализатор
  static dynamic _writePaymentData(PaymentData? data) => data?.toJson();
}

//MARK: - Response
@JsonSerializable()
class ResponsePayment {
  final String? id;
  final Amount? amount;
  final String? status;
  @JsonKey(name: "created_at")
  final DateTime? createdAt;
  @JsonKey(name: "payment_id")
  final String? paymentId;
  final bool? done;

  ResponsePayment({
    this.id,
    this.amount,
    this.status,
    this.createdAt,
    this.paymentId,
    this.done,
  });

  factory ResponsePayment.fromJson(Map<String, dynamic> json) => _$ResponsePaymentFromJson(json);
  Map<String, dynamic> toJson() => _$ResponsePaymentToJson(this);
}

//MARK: - Amount
@JsonSerializable()
class Amount {
  @JsonKey(fromJson: _stringToDouble, toJson: _doubleToString)
  final double? value;
  final String? currency;

  Amount({
    this.value,
    this.currency,
  });

  factory Amount.fromJson(Map<String, dynamic> json) => _$AmountFromJson(json);
  Map<String, dynamic> toJson() => _$AmountToJson(this);

  // Кастомные функции для преобразования
  static double? _stringToDouble(dynamic value) {
    if (value == null) return null;
    if (value is num) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  static String? _doubleToString(double? value) => value?.toString();
}

//MARK: - PaymentType
@JsonSerializable()
class PaymentType {
  final int? id;
  final String? title;

  PaymentType({
    this.id,
    this.title,
  });

  factory PaymentType.fromJson(Map<String, dynamic> json) => _$PaymentTypeFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentTypeToJson(this);
}

//MARK: - OrderType
@JsonSerializable()
class OrderType {
  final int? id;
  final String? title;

  OrderType({this.id, this.title});

  factory OrderType.fromJson(Map<String, dynamic> json) => _$OrderTypeFromJson(json);
  Map<String, dynamic> toJson() => _$OrderTypeToJson(this);
}

//MARK - Status
@JsonSerializable()
class Status {
  @JsonKey(fromJson: _idFromJson, toJson: _idToJson)
  final String id;
  final String title;

  Status({required this.id, required this.title});

  factory Status.fromJson(Map<String, dynamic> json) => _$StatusFromJson(json);
  Map<String, dynamic> toJson() => _$StatusToJson(this);

  // Кастомные функции для преобразования
  static String _idFromJson(dynamic id) => id.toString();
  static dynamic _idToJson(String id) => id;
}

//MARK - TripPayElement
@JsonSerializable()
class TripPayElement {
  final int? id;
  final int? count;
  final int? price;
  @JsonKey(name: "sum_all")
  final int? sumAll;
  final Status? status;
  final TripPay? trip;
  final List<Ticket>? tickets;
  final dynamic errors;
  @JsonKey(name: "checks_online")
  final List<dynamic>? checksOnline;

  TripPayElement({
    this.id,
    this.count,
    this.price,
    this.sumAll,
    this.status,
    this.trip,
    this.tickets,
    this.errors,
    this.checksOnline,
  });

  factory TripPayElement.fromJson(Map<String, dynamic> json) => _$TripPayElementFromJson(json);
  Map<String, dynamic> toJson() => _$TripPayElementToJson(this);
}

@JsonSerializable()
class TripPay {
  final int? id;
  @JsonKey(name: "external_id")
  final String? externalId;
  @JsonKey(name: "point_a")
  final Point? pointA;
  @JsonKey(name: "point_b")
  final Point? pointB;
  final String? start;
  final String? end;
  @JsonKey(name: "trip_id")
  final String? tripId;
  final String? route;
  final String? carrier;
  final String? platform;
  @JsonKey(name: "travel_time")
  final int? travelTime;
  @JsonKey(name: "travel_time_human")
  final String? travelTimeHuman;
  final String? title;

  TripPay({
    this.id,
    this.externalId,
    this.pointA,
    this.pointB,
    this.start,
    this.end,
    this.tripId,
    this.route,
    this.carrier,
    this.platform,
    this.travelTime,
    this.travelTimeHuman,
    this.title,
  });

  factory TripPay.fromJson(Map<String, dynamic> json) => _$TripPayFromJson(json);
  Map<String, dynamic> toJson() => _$TripPayToJson(this);
}

@JsonSerializable()
class Point {
  final int? id;
  final String? title;
  final String? timezone;

  Point({this.id, this.title, this.timezone});

  factory Point.fromJson(Map<String, dynamic> json) => _$PointFromJson(json);
  Map<String, dynamic> toJson() => _$PointToJson(this);
}

@JsonSerializable()
class Ticket {
  final int? id;
  final Status? status;
  final String? url;
  @JsonKey(name: "return_ticket")
  final ReturnTicket? returnTicket;
  @JsonKey(name: "external_id")
  final String? externalId;
  @JsonKey(name: "created_at")
  final String? createdAt;
  @JsonKey(name: "updated_at")
  final String? updatedAt;
  final String? title;
  @JsonKey(name: "is_active")
  final bool? isActive;
  final String? date;
  @JsonKey(name: "date_reserve")
  final String? dateReserve;
  @JsonKey(name: "ticket_number")
  final String? ticketNumber;
  @JsonKey(name: "place_number")
  final String? placeNumber;
  @JsonKey(name: "is_stand")
  final bool? isStand;
  @JsonKey(name: "is_child")
  final bool? isChild;
  @JsonKey(name: "is_baggage")
  final bool? isBaggage;
  final dynamic barcode;
  @JsonKey(name: "is_return")
  final bool? isReturn;
  @JsonKey(name: "commission_value")
  final int? commissionValue;
  final int? price;
  @JsonKey(name: "price_base")
  final int? priceBase;
  @JsonKey(name: "price_baggage")
  final int? priceBaggage;
  @JsonKey(name: "price_baggage_base")
  final int? priceBaggageBase;
  @JsonKey(name: "price_dues")
  final int? priceDues;
  @JsonKey(name: "sum_baggage")
  final int? sumBaggage;
  @JsonKey(name: "sum_baggage_base")
  final int? sumBaggageBase;
  @JsonKey(name: "count_baggage")
  final int? countBaggage;
  @JsonKey(name: "external_id_baggage")
  final List<dynamic>? externalIdBaggage;
  @JsonKey(name: "sum_all")
  final int? sumAll;
  @JsonKey(name: "sum_all_base")
  final int? sumAllBase;
  @JsonKey(name: "sum_insurance")
  final int? sumInsurance;
  final String? uuid;
  @JsonKey(name: "is_external")
  final bool? isExternal;
  @JsonKey(name: "external_data")
  final Map<String, dynamic>? externalData;
  final int? company;
  @JsonKey(name: "pass_ticket")
  final dynamic passTicket;
  @PassengerConverter()
  final Passenger? passenger;
  final int? customer;
  final Trip? trip;
  @JsonKey(name: "payment_button")
  PaymentButton? paymentButton;

  final isSelected = false.obs;

  Ticket({
    this.id,
    this.status,
    this.url,
    this.returnTicket,
    this.externalId,
    this.createdAt,
    this.updatedAt,
    this.title,
    this.isActive,
    this.date,
    this.dateReserve,
    this.ticketNumber,
    this.placeNumber,
    this.isStand,
    this.isChild,
    this.isBaggage,
    this.barcode,
    this.isReturn,
    this.commissionValue,
    this.price,
    this.priceBase,
    this.priceBaggage,
    this.priceBaggageBase,
    this.priceDues,
    this.sumBaggage,
    this.sumBaggageBase,
    this.countBaggage,
    this.externalIdBaggage,
    this.sumAll,
    this.sumAllBase,
    this.sumInsurance,
    this.uuid,
    this.isExternal,
    this.externalData,
    this.company,
    this.passTicket,
    this.passenger,
    this.customer,
    this.trip,
  });

  bool get isReturned => returnTicket?.status?.id == "2";

  factory Ticket.fromRawJson(String str) => Ticket.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory Ticket.fromJson(Map<String, dynamic> json) => _$TicketFromJson(json);
  Map<String, dynamic> toJson() => _$TicketToJson(this);
}

@JsonSerializable()
class ReturnTicket {
  final Status? status;
  final String? date;
  @JsonKey(name: "return_type")
  final String? returnType;
  @JsonKey(name: "sum_return")
  final double? sumReturn;
  final String? errors;
  final double? order;
  @JsonKey(name: "order_id")
  final double? orderId;

  ReturnTicket({
    this.status,
    this.date,
    this.returnType,
    this.sumReturn,
    this.errors,
    this.order,
    this.orderId,
  });

  factory ReturnTicket.fromRawJson(String str) => ReturnTicket.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory ReturnTicket.fromJson(Map<String, dynamic> json) => _$ReturnTicketFromJson(json);
  Map<String, dynamic> toJson() => _$ReturnTicketToJson(this);
}

class PassengerConverter implements JsonConverter<Passenger?, dynamic> {
  const PassengerConverter();

  @override
  Passenger? fromJson(dynamic json) {
    if (json is Map<String, dynamic>) {
      return Passenger.fromJson(json);
    }
    return null;
  }

  @override
  dynamic toJson(Passenger? passenger) {
    return passenger?.toJson();
  }
}
