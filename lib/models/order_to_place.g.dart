// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_to_place.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderToPlace _$OrderToPlaceFromJson(Map<String, dynamic> json) => OrderToPlace(
      buyerEmail: json['buyer_email'] as String?,
      buyerPhone: json['buyer_phone'] as String?,
      sendSms: json['send_sms'] as bool?,
      paymentType: json['payment_type'] as String?,
      trips: (json['trips'] as List<dynamic>?)
          ?.map((e) => TripElement.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$OrderToPlaceToJson(OrderToPlace instance) =>
    <String, dynamic>{
      'buyer_email': instance.buyerEmail,
      'buyer_phone': instance.buyerPhone,
      'send_sms': instance.sendSms,
      'payment_type': instance.paymentType,
      'trips': instance.trips,
    };

TripElement _$TripElementFromJson(Map<String, dynamic> json) => TripElement(
      trip: json['trip'] == null
          ? null
          : TripTrip.fromJson(json['trip'] as Map<String, dynamic>),
      tickets: (json['tickets'] as List<dynamic>?)
          ?.map((e) => TicketOrder.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TripElementToJson(TripElement instance) =>
    <String, dynamic>{
      'trip': instance.trip,
      'tickets': instance.tickets,
    };

TicketOrder _$TicketOrderFromJson(Map<String, dynamic> json) => TicketOrder(
      citizenship: (json['citizenship'] as num?)?.toInt(),
      typeDocument: (json['type_document'] as num?)?.toInt(),
      seriaNumber: json['seria_number'] as String?,
      lastName: json['last_name'] as String?,
      firstName: json['first_name'] as String?,
      isChild: json['is_child'] as bool?,
      secondName: json['second_name'] as String?,
      sex: (json['sex'] as num?)?.toInt(),
      dateOfBirthday: json['date_of_birthday'] as String?,
      placeNumber: (json['place_number'] as num?)?.toInt(),
      countBaggage: (json['count_baggage'] as num?)?.toInt(),
      insurance: json['insurance'] as bool?,
    );

Map<String, dynamic> _$TicketOrderToJson(TicketOrder instance) =>
    <String, dynamic>{
      'citizenship': instance.citizenship,
      'type_document': instance.typeDocument,
      'seria_number': instance.seriaNumber,
      'last_name': instance.lastName,
      'first_name': instance.firstName,
      'is_child': instance.isChild,
      'second_name': instance.secondName,
      'sex': instance.sex,
      'date_of_birthday': instance.dateOfBirthday,
      'place_number': instance.placeNumber,
      'count_baggage': instance.countBaggage,
      'insurance': instance.insurance,
    };

TripTrip _$TripTripFromJson(Map<String, dynamic> json) => TripTrip(
      pointA: (json['point_a'] as num?)?.toInt(),
      start: json['start'] == null
          ? null
          : DateTime.parse(json['start'] as String),
      tripId: json['trip_id'] as String?,
      pointB: (json['point_b'] as num?)?.toInt(),
      withoutPd: json['without_pd'] as bool?,
    );

Map<String, dynamic> _$TripTripToJson(TripTrip instance) => <String, dynamic>{
      'point_a': instance.pointA,
      'start': instance.start?.toIso8601String(),
      'trip_id': instance.tripId,
      'point_b': instance.pointB,
      'without_pd': instance.withoutPd,
    };
