import 'package:json_annotation/json_annotation.dart';

part 'order_to_place.g.dart';

@JsonSerializable()
class OrderToPlace {
  @JsonKey(name: "buyer_email")
  String? buyerEmail;
  @Json<PERSON>ey(name: "buyer_phone")
  String? buyerPhone;
  @Json<PERSON>ey(name: "send_sms")
  bool? sendSms;
  @Json<PERSON>ey(name: "payment_type")
  String? paymentType;
  List<TripElement>? trips;

  OrderToPlace({
    this.buyerEmail,
    this.buyerPhone,
    this.sendSms,
    this.paymentType,
    this.trips,
  });

  factory OrderToPlace.fromJson(Map<String, dynamic> json) => _$OrderToPlaceFromJson(json);
  Map<String, dynamic> toJson() => _$OrderToPlaceToJson(this);
}

@JsonSerializable()
class TripElement {
  final TripTrip? trip;
  final List<TicketOrder>? tickets;

  TripElement({
    this.trip,
    this.tickets,
  });

  factory TripElement.fromJson(Map<String, dynamic> json) => _$TripElementFromJson(json);
  Map<String, dynamic> toJson() => _$TripElementToJson(this);
}

@JsonSerializable()
class TicketOrder {
  final int? citizenship;
  @JsonKey(name: "type_document")
  final int? typeDocument;
  @JsonKey(name: "seria_number")
  final String? seriaNumber;
  @JsonKey(name: "last_name")
  final String? lastName;
  @JsonKey(name: "first_name")
  final String? firstName;
  @JsonKey(name: "is_child")
  bool? isChild;
  @JsonKey(name: "second_name")
  final String? secondName;
  final int? sex;
  @JsonKey(name: "date_of_birthday")
  final String? dateOfBirthday;
  @JsonKey(name: "place_number")
  int? placeNumber;
  @JsonKey(name: "count_baggage")
  final int? countBaggage;
  final bool? insurance;

  TicketOrder({
    this.citizenship,
    this.typeDocument,
    this.seriaNumber,
    this.lastName,
    this.firstName,
    this.isChild,
    this.secondName,
    this.sex,
    this.dateOfBirthday,
    this.placeNumber,
    this.countBaggage,
    this.insurance,
  });

  factory TicketOrder.fromJson(Map<String, dynamic> json) => _$TicketOrderFromJson(json);
  Map<String, dynamic> toJson() => _$TicketOrderToJson(this);
}

@JsonSerializable()
class TripTrip {
  @JsonKey(name: "point_a")
  final int? pointA;
  final DateTime? start;
  @JsonKey(name: "trip_id")
  final String? tripId;
  @JsonKey(name: "point_b")
  final int? pointB;
  @JsonKey(name: "without_pd")
  final bool? withoutPd;

  TripTrip({
    this.pointA,
    this.start,
    this.tripId,
    this.pointB,
    this.withoutPd,
  });

  factory TripTrip.fromJson(Map<String, dynamic> json) => _$TripTripFromJson(json);
  Map<String, dynamic> toJson() => _$TripTripToJson(this);
}
