import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'config_model.g.dart';

@JsonSerializable()
class Config {
  @Json<PERSON>ey(name: "default_dispatch")
  final DefaultDispatch? defaultDispatch;
  final String? error;

  Config({
    this.defaultDispatch,
    this.error,
  });

  factory Config.fromRawJson(String str) => Config.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());
  factory Config.fromJson(Map<String, dynamic> json) => _$ConfigFromJson(json);
  Map<String, dynamic> toJson() => _$ConfigToJson(this);
}

@JsonSerializable()
class DefaultDispatch {
  final String? id;
  final String? title;
  @JsonKey(name: "is_popular")
  final bool? isPopular;
  @Json<PERSON>ey(name: "is_catalog")
  final bool? isCatalog;
  final String? address;

  DefaultDispatch({
    this.id,
    this.title,
    this.isPopular,
    this.isCatalog,
    this.address,
  });

  factory DefaultDispatch.fromJson(Map<String, dynamic> json) =>
      _$DefaultDispatchFromJson(json);
  Map<String, dynamic> toJson() => _$DefaultDispatchToJson(this);
}
