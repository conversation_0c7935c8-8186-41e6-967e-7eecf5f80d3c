import 'package:json_annotation/json_annotation.dart';
import 'package:timezone/timezone.dart' as tz;

part 'trip_model.g.dart';

@JsonSerializable()
class Trip {
  @JsonKey(name: "point_a")
  StationPoint? pointA;
  @Json<PERSON>ey(name: "point_b")
  StationPoint? pointB;
  DateTime? start;
  @J<PERSON><PERSON><PERSON>(name: "trip_id")
  String? tripId;
  String? route;
  bool? concreate;
  DateTime? end;
  dynamic distance;
  @<PERSON>son<PERSON><PERSON>(name: "free_place")
  int? freePlace;
  @JsonKey(name: "free_places")
  List<int>? freePlaces;
  @Json<PERSON><PERSON>(name: "price_full")
  double? priceFull;
  @Json<PERSON>ey(name: "price_child")
  double? priceChild;
  @<PERSON><PERSON><PERSON><PERSON>(name: "price_baggage")
  double? priceBaggage;
  @JsonKey(name: "price_insurance")
  double? priceInsurance;
  @<PERSON>sonKey(name: "price_dues")
  double? priceDues;
  @<PERSON><PERSON><PERSON><PERSON>(name: "bus_info")
  String? busInfo;
  String? number;
  @<PERSON><PERSON><PERSON><PERSON>(name: "travel_time")
  int? travelTime;
  @J<PERSON><PERSON><PERSON>(name: "travel_time_human")
  String? travelTimeHuman;
  String? carrier;
  String? schedule;
  String? platform;
  @JsonKey(name: "without_pd")
  bool? withoutPd;
  @JsonKey(name: "without_select_places")
  bool? withoutSelectPlaces;
  dynamic status;
  @JsonKey(name: "sale_status")
  dynamic saleStatus;
  @JsonKey(name: "available_for_sale")
  bool? availableForSale;
  int? commission;
  @JsonKey(name: "child_tariff_name")
  dynamic childTariffName;
  @JsonKey(name: "child_age_from")
  dynamic childAgeFrom;
  @JsonKey(name: "child_age_to")
  dynamic childAgeTo;
  @JsonKey(name: "notice_info")
  NoticeInfo? noticeInfo;
  @JsonKey(name: "insurance_info")
  InsuranceInfo? insuranceInfo;
  @JsonKey(name: "commission_info")
  CommissionInfo? commissionInfo;

  Trip({
    this.pointA,
    this.pointB,
    this.start,
    this.tripId,
    this.route,
    this.concreate,
    this.end,
    this.distance,
    this.freePlace,
    this.freePlaces,
    this.priceFull,
    this.priceChild,
    this.priceBaggage,
    this.priceInsurance,
    this.priceDues,
    this.busInfo,
    this.number,
    this.travelTime,
    this.travelTimeHuman,
    this.carrier,
    this.schedule,
    this.platform,
    this.withoutPd,
    this.withoutSelectPlaces,
    this.status,
    this.saleStatus,
    this.availableForSale,
    this.commission,
    this.childTariffName,
    this.childAgeFrom,
    this.childAgeTo,
    this.noticeInfo,
    this.insuranceInfo,
    this.commissionInfo,
  });

  String get startTimeString => _localTimeString(pointA, start);
  String get endTimeString => _localTimeString(pointB, end);

  String _localTimeString(StationPoint? point, DateTime? date) {
    if (date == null || point == null) return "";

    final timezone = point.timezone ?? "Europe/Moscow";
    final location = tz.getLocation(timezone);

    DateTime res = tz.TZDateTime.from(date, location);
    return "${res.hour.toString().padLeft(2, '0')}:${res.minute.toString().padLeft(2, '0')}";
  }

  factory Trip.fromJson(Map<String, dynamic> json) => _$TripFromJson(json);
  Map<String, dynamic> toJson() => _$TripToJson(this);
}

@JsonSerializable()
class CommissionInfo {
  double? adult;
  double? child;
  double? baggage;

  CommissionInfo({
    this.adult,
    this.child,
    this.baggage,
  });

  factory CommissionInfo.fromJson(Map<String, dynamic> json) =>
      _$CommissionInfoFromJson(json);
  Map<String, dynamic> toJson() => _$CommissionInfoToJson(this);
}

@JsonSerializable()
class InsuranceInfo {
  @JsonKey(name: "insurance_available")
  bool? insuranceAvailable;
  @JsonKey(name: "use_insurance_default")
  bool? useInsuranceDefault;
  @JsonKey(name: "price_insurer_adult")
  double? priceInsurerAdult;
  @JsonKey(name: "price_insurer_child")
  double? priceInsurerChild;
  @JsonKey(name: "insurer_rules")
  String? insurerRules;

  InsuranceInfo({
    this.insuranceAvailable,
    this.useInsuranceDefault,
    this.priceInsurerAdult,
    this.priceInsurerChild,
    this.insurerRules,
  });

  factory InsuranceInfo.fromJson(Map<String, dynamic> json) =>
      _$InsuranceInfoFromJson(json);
  Map<String, dynamic> toJson() => _$InsuranceInfoToJson(this);
}

@JsonSerializable()
class NoticeInfo {
  @JsonKey(name: "notice_available")
  bool? noticeAvailable;
  @JsonKey(name: "use_notice_default")
  bool? useNoticeDefault;
  @JsonKey(name: "price_notice")
  double? priceNotice;

  NoticeInfo({
    this.noticeAvailable,
    this.useNoticeDefault,
    this.priceNotice,
  });

  factory NoticeInfo.fromJson(Map<String, dynamic> json) =>
      _$NoticeInfoFromJson(json);
  Map<String, dynamic> toJson() => _$NoticeInfoToJson(this);
}

@JsonSerializable()
class StationPoint {
  int? id;
  String? title;
  String? timezone;

  StationPoint({
    this.id,
    this.title,
    this.timezone,
  });

  factory StationPoint.fromJson(Map<String, dynamic> json) =>
      _$StationPointFromJson(json);
  Map<String, dynamic> toJson() => _$StationPointToJson(this);
}
