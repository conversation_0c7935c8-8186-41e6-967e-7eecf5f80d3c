// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Config _$Config<PERSON>rom<PERSON>(Map<String, dynamic> json) => Config(
      defaultDispatch: json['default_dispatch'] == null
          ? null
          : DefaultDispatch.fromJson(
              json['default_dispatch'] as Map<String, dynamic>),
      error: json['error'] as String?,
    );

Map<String, dynamic> _$ConfigTo<PERSON>son(Config instance) => <String, dynamic>{
      'default_dispatch': instance.defaultDispatch,
      'error': instance.error,
    };

DefaultDispatch _$DefaultDispatchFromJson(Map<String, dynamic> json) =>
    DefaultDispatch(
      id: json['id'] as String?,
      title: json['title'] as String?,
      isPopular: json['is_popular'] as bool?,
      isCatalog: json['is_catalog'] as bool?,
      address: json['address'] as String?,
    );

Map<String, dynamic> _$DefaultDispatchToJson(DefaultDispatch instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'is_popular': instance.isPopular,
      'is_catalog': instance.isCatalog,
      'address': instance.address,
    };
