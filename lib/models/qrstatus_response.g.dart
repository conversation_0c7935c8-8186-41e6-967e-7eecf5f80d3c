// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'qrstatus_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QRAuthStatusResponse _$QRAuthStatusResponseFromJson(
        Map<String, dynamic> json) =>
    QRAuthStatusResponse(
      uuid: json['uuid'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      expiredAt: json['expired_at'] == null
          ? null
          : DateTime.parse(json['expired_at'] as String),
      status: json['status'] == null
          ? null
          : Status.fromJson(json['status'] as Map<String, dynamic>),
      customer: json['customer'] == null
          ? null
          : Customer.fromJson(json['customer'] as Map<String, dynamic>),
      url: json['url'] as String?,
    );

Map<String, dynamic> _$QRAuthStatusResponseToJson(
        QRAuthStatusResponse instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'created_at': instance.createdAt?.toIso8601String(),
      'expired_at': instance.expiredAt?.toIso8601String(),
      'status': instance.status,
      'customer': instance.customer,
      'url': instance.url,
    };

Customer _$CustomerFromJson(Map<String, dynamic> json) => Customer(
      id: (json['id'] as num?)?.toInt(),
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      passengers: (json['passengers'] as List<dynamic>?)
          ?.map((e) => Passenger.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CustomerToJson(Customer instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'phone': instance.phone,
      'passengers': instance.passengers,
    };

Passenger _$PassengerFromJson(Map<String, dynamic> json) => Passenger(
      id: (json['id'] as num?)?.toInt(),
      lastName: json['last_name'] as String?,
      firstName: json['first_name'] as String?,
      secondName: json['second_name'] as String?,
      sex: (json['sex'] as num?)?.toInt(),
      dateOfBirthday: json['date_of_birthday'] as String?,
      citizenship: json['citizenship'] == null
          ? null
          : Country.fromJson(json['citizenship'] as Map<String, dynamic>),
      country: json['country'] == null
          ? null
          : Country.fromJson(json['country'] as Map<String, dynamic>),
      typeDocument: json['type_document'] == null
          ? null
          : DocType.fromJson(json['type_document'] as Map<String, dynamic>),
      seriaNumber: json['seria_number'] as String?,
      customer: (json['customer'] as num?)?.toInt(),
      isActive: json['is_active'] as bool?,
    )..error = json['error'] as String?;

Map<String, dynamic> _$PassengerToJson(Passenger instance) => <String, dynamic>{
      'id': instance.id,
      'last_name': instance.lastName,
      'first_name': instance.firstName,
      'second_name': instance.secondName,
      'sex': instance.sex,
      'date_of_birthday': instance.dateOfBirthday,
      'citizenship': instance.citizenship,
      'country': instance.country,
      'type_document': instance.typeDocument,
      'seria_number': instance.seriaNumber,
      'customer': instance.customer,
      'is_active': instance.isActive,
      'error': instance.error,
    };

Status _$StatusFromJson(Map<String, dynamic> json) => Status(
      id: json['id'] as String?,
      title: json['title'] as String?,
    );

Map<String, dynamic> _$StatusToJson(Status instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
    };
