// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trip_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TripDetail _$TripDetailFromJson(Map<String, dynamic> json) => TripDetail(
      tripId: json['trip_id'] as String?,
      pointA: json['point_a'] == null
          ? null
          : StationPoint.fromJson(json['point_a'] as Map<String, dynamic>),
      pointB: json['point_b'] == null
          ? null
          : StationPoint.fromJson(json['point_b'] as Map<String, dynamic>),
      start: json['start'] == null
          ? null
          : DateTime.parse(json['start'] as String),
      end: json['end'] == null ? null : DateTime.parse(json['end'] as String),
      route: json['route'] as String?,
      status: json['status'],
      saleStatus: json['sale_status'],
      availableForSale: json['available_for_sale'] as bool?,
      stationForSale: json['station_for_sale'],
      priceFull: (json['price_full'] as num?)?.toDouble(),
      priceChild: (json['price_child'] as num?)?.toDouble(),
      priceBaggage: (json['price_baggage'] as num?)?.toDouble(),
      priceInsurance: (json['price_insurance'] as num?)?.toDouble(),
      priceDues: (json['price_dues'] as num?)?.toDouble(),
      commission: (json['commission'] as num?)?.toDouble(),
      childTariffName: json['child_tariff_name'],
      childAgeFrom: json['child_age_from'],
      childAgeTo: json['child_age_to'],
      distance: json['distance'],
      travelTime: (json['travel_time'] as num?)?.toInt(),
      travelTimeHuman: json['travel_time_human'] as String?,
      schedule: json['schedule'] as String?,
      platform: json['platform'] as String?,
      carrier: json['carrier'] as String?,
      number: json['number'] as String?,
      busInfo: json['bus_info'] as String?,
      freePlace: (json['free_place'] as num?)?.toInt(),
      freePlaces: (json['free_places'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      seatsSchema: (json['seats_schema'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(
            k,
            (e as Map<String, dynamic>).map(
              (k, e) =>
                  MapEntry(k, SeatsSchema.fromJson(e as Map<String, dynamic>)),
            )),
      ),
      withoutPd: json['without_pd'] as bool?,
      withoutSelectPlaces: json['without_select_places'] as bool?,
      noticeInfo: json['notice_info'] == null
          ? null
          : NoticeInfo.fromJson(json['notice_info'] as Map<String, dynamic>),
      insuranceInfo: json['insurance_info'] == null
          ? null
          : InsuranceInfo.fromJson(
              json['insurance_info'] as Map<String, dynamic>),
      commissionInfo: json['commission_info'] == null
          ? null
          : CommissionInfo.fromJson(
              json['commission_info'] as Map<String, dynamic>),
    )..paymentButtons = (json['payment_buttons'] as List<dynamic>?)
        ?.map((e) => PaymentButton.fromJson(e as Map<String, dynamic>))
        .toList();

Map<String, dynamic> _$TripDetailToJson(TripDetail instance) =>
    <String, dynamic>{
      'trip_id': instance.tripId,
      'point_a': instance.pointA,
      'point_b': instance.pointB,
      'start': instance.start?.toIso8601String(),
      'end': instance.end?.toIso8601String(),
      'route': instance.route,
      'status': instance.status,
      'sale_status': instance.saleStatus,
      'available_for_sale': instance.availableForSale,
      'station_for_sale': instance.stationForSale,
      'price_full': instance.priceFull,
      'price_child': instance.priceChild,
      'price_baggage': instance.priceBaggage,
      'price_insurance': instance.priceInsurance,
      'price_dues': instance.priceDues,
      'commission': instance.commission,
      'child_tariff_name': instance.childTariffName,
      'child_age_from': instance.childAgeFrom,
      'child_age_to': instance.childAgeTo,
      'distance': instance.distance,
      'travel_time': instance.travelTime,
      'travel_time_human': instance.travelTimeHuman,
      'schedule': instance.schedule,
      'platform': instance.platform,
      'carrier': instance.carrier,
      'number': instance.number,
      'bus_info': instance.busInfo,
      'free_place': instance.freePlace,
      'free_places': instance.freePlaces,
      'seats_schema': instance.seatsSchema,
      'without_pd': instance.withoutPd,
      'without_select_places': instance.withoutSelectPlaces,
      'notice_info': instance.noticeInfo,
      'insurance_info': instance.insuranceInfo,
      'commission_info': instance.commissionInfo,
      'payment_buttons': instance.paymentButtons,
    };

CommissionInfo _$CommissionInfoFromJson(Map<String, dynamic> json) =>
    CommissionInfo(
      adult: (json['adult'] as num?)?.toDouble(),
      child: (json['child'] as num?)?.toDouble(),
      baggage: (json['baggage'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$CommissionInfoToJson(CommissionInfo instance) =>
    <String, dynamic>{
      'adult': instance.adult,
      'child': instance.child,
      'baggage': instance.baggage,
    };

InsuranceInfo _$InsuranceInfoFromJson(Map<String, dynamic> json) =>
    InsuranceInfo(
      insuranceAvailable: json['insurance_available'] as bool?,
      useInsuranceDefault: json['use_insurance_default'] as bool?,
      priceInsurerAdult: (json['price_insurer_adult'] as num?)?.toDouble(),
      priceInsurerChild: (json['price_insurer_child'] as num?)?.toDouble(),
      insurerRules: json['insurer_rules'] as String?,
    );

Map<String, dynamic> _$InsuranceInfoToJson(InsuranceInfo instance) =>
    <String, dynamic>{
      'insurance_available': instance.insuranceAvailable,
      'use_insurance_default': instance.useInsuranceDefault,
      'price_insurer_adult': instance.priceInsurerAdult,
      'price_insurer_child': instance.priceInsurerChild,
      'insurer_rules': instance.insurerRules,
    };

NoticeInfo _$NoticeInfoFromJson(Map<String, dynamic> json) => NoticeInfo(
      noticeAvailable: json['notice_available'] as bool?,
      useNoticeDefault: json['use_notice_default'] as bool?,
      priceNotice: (json['price_notice'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$NoticeInfoToJson(NoticeInfo instance) =>
    <String, dynamic>{
      'notice_available': instance.noticeAvailable,
      'use_notice_default': instance.useNoticeDefault,
      'price_notice': instance.priceNotice,
    };

SeatsSchema _$SeatsSchemaFromJson(Map<String, dynamic> json) => SeatsSchema(
      number: (json['number'] as num?)?.toInt(),
      status: $enumDecodeNullable(_$StatusEnumMap, json['status']),
    );

Map<String, dynamic> _$SeatsSchemaToJson(SeatsSchema instance) =>
    <String, dynamic>{
      'number': instance.number,
      'status': _$StatusEnumMap[instance.status],
    };

const _$StatusEnumMap = {
  Status.BUSY: 'busy',
  Status.EMPTY: 'empty',
  Status.FREE: 'free',
};

PaymentButton _$PaymentButtonFromJson(Map<String, dynamic> json) =>
    PaymentButton(
      id: (json['id'] as num?)?.toInt(),
      slug: json['slug'] as String?,
    );

Map<String, dynamic> _$PaymentButtonToJson(PaymentButton instance) =>
    <String, dynamic>{
      'id': instance.id,
      'slug': instance.slug,
    };
