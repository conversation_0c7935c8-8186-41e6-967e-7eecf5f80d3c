// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_payment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreatePaymentModel _$CreatePaymentModelFromJson(Map<String, dynamic> json) =>
    CreatePaymentModel(
      id: (json['id'] as num?)?.toInt(),
      status: json['status'] == null
          ? null
          : Status.fromJson(json['status'] as Map<String, dynamic>),
      errors: json['errors'] as String?,
      redirectUrl: json['redirect_url'] as String?,
      data: CreatePaymentModel._parsePaymentData(json['data']),
    );

Map<String, dynamic> _$CreatePaymentModelToJson(CreatePaymentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'status': instance.status,
      'errors': instance.errors,
      'redirect_url': instance.redirectUrl,
      'data': CreatePaymentModel._writePaymentData(instance.data),
    };

PaymentData _$PaymentDataFromJson(Map<String, dynamic> json) => PaymentData(
      url: json['url'] as String?,
      method: json['method'] as String?,
      command: json['command'] == null
          ? null
          : PaymentCommand.fromJson(json['command'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PaymentDataToJson(PaymentData instance) =>
    <String, dynamic>{
      'url': instance.url,
      'method': instance.method,
      'command': instance.command,
    };

PaymentCommand _$PaymentCommandFromJson(Map<String, dynamic> json) =>
    PaymentCommand(
      amount: (json['Amount'] as num?)?.toDouble(),
      command: json['Command'] as String?,
      idDevice: (json['IdDevice'] as num?)?.toInt(),
      idCommand: json['IdCommand'] as String?,
      numDevice: (json['NumDevice'] as num?)?.toInt(),
      universalID: json['UniversalID'] as String?,
    );

Map<String, dynamic> _$PaymentCommandToJson(PaymentCommand instance) =>
    <String, dynamic>{
      if (instance.amount case final value?) 'Amount': value,
      if (instance.command case final value?) 'Command': value,
      if (instance.idDevice case final value?) 'IdDevice': value,
      if (instance.idCommand case final value?) 'IdCommand': value,
      if (instance.numDevice case final value?) 'NumDevice': value,
      if (instance.universalID case final value?) 'UniversalID': value,
    };
