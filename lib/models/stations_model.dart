import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'stations_model.g.dart';

@JsonSerializable()
class Stations {
  List<Station>? stations;

  Stations({
    this.stations,
  });

  factory Stations.fromJson(Map<String, dynamic> json) =>
      _$StationsFromJson(json);
  Map<String, dynamic> toJson() => _$StationsToJson(this);
}

@JsonSerializable()
class Station {
  String? id;
  String? title;
  @JsonKey(name: "is_popular")
  bool? isPopular;
  @JsonKey(name: "is_catalog")
  bool? isCatalog;
  String? address;
  @Json<PERSON>ey(name: "catalog_id")
  String? catalogId;

  Station({
    this.id,
    this.title,
    this.isPopular,
    this.isCatalog,
    this.address,
    this.catalogId,
  });

  factory Station.fromRawJson(String str) => Station.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory Station.fromJson(Map<String, dynamic> json) =>
      _$StationFrom<PERSON>son(json);
  Map<String, dynamic> toJson() => _$StationToJson(this);
}
