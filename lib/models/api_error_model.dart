import 'package:json_annotation/json_annotation.dart';

part 'api_error_model.g.dart';

@JsonSerializable()
class ApiError {
  @JsonKey(name: "exception_type")
  final String? exceptionType;
  final String message;
  final int? code;

  ApiError({this.exceptionType, required this.message, this.code});

  factory ApiError.fromJson(Map<String, dynamic> json) => _$ApiErrorFromJson(json);

  Map<String, dynamic> toJson() => _$ApiErrorToJson(this);
}
