import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
import 'package:terminal/models/trip_model.dart';

part 'trip_detail_model.g.dart';

@JsonSerializable()
class TripDetail {
  @J<PERSON><PERSON><PERSON>(name: "trip_id")
  String? tripId;
  @Json<PERSON>ey(name: "point_a")
  StationPoint? pointA;
  @JsonKey(name: "point_b")
  StationPoint? pointB;
  DateTime? start;
  DateTime? end;
  String? route;
  dynamic status;
  @<PERSON><PERSON><PERSON><PERSON>(name: "sale_status")
  dynamic saleStatus;
  @Json<PERSON>ey(name: "available_for_sale")
  bool? availableForSale;
  @Json<PERSON>ey(name: "station_for_sale")
  dynamic stationForSale;
  @JsonKey(name: "price_full")
  double? priceFull;
  @Json<PERSON>ey(name: "price_child")
  double? priceChild;
  @Json<PERSON>ey(name: "price_baggage")
  double? priceBaggage;
  @JsonKey(name: "price_insurance")
  double? priceInsurance;
  @<PERSON><PERSON><PERSON>ey(name: "price_dues")
  double? priceDues;
  double? commission;
  @Json<PERSON>ey(name: "child_tariff_name")
  dynamic childTariffName;
  @JsonKey(name: "child_age_from")
  dynamic childAgeFrom;
  @JsonKey(name: "child_age_to")
  dynamic childAgeTo;
  dynamic distance;
  @JsonKey(name: "travel_time")
  int? travelTime;
  @JsonKey(name: "travel_time_human")
  String? travelTimeHuman;
  String? schedule;
  String? platform;
  String? carrier;
  String? number;
  @JsonKey(name: "bus_info")
  String? busInfo;
  @JsonKey(name: "free_place")
  int? freePlace;
  @JsonKey(name: "free_places")
  List<int>? freePlaces;
  @JsonKey(name: "seats_schema")
  Map<String, Map<String, SeatsSchema>>? seatsSchema;
  @JsonKey(name: "without_pd")
  bool? withoutPd;
  @JsonKey(name: "without_select_places")
  bool? withoutSelectPlaces;
  @JsonKey(name: "notice_info")
  NoticeInfo? noticeInfo;
  @JsonKey(name: "insurance_info")
  InsuranceInfo? insuranceInfo;
  @JsonKey(name: "commission_info")
  CommissionInfo? commissionInfo;
  @JsonKey(name: "payment_buttons")
  List<PaymentButton>? paymentButtons = [];

  TripDetail({
    this.tripId,
    this.pointA,
    this.pointB,
    this.start,
    this.end,
    this.route,
    this.status,
    this.saleStatus,
    this.availableForSale,
    this.stationForSale,
    this.priceFull,
    this.priceChild,
    this.priceBaggage,
    this.priceInsurance,
    this.priceDues,
    this.commission,
    this.childTariffName,
    this.childAgeFrom,
    this.childAgeTo,
    this.distance,
    this.travelTime,
    this.travelTimeHuman,
    this.schedule,
    this.platform,
    this.carrier,
    this.number,
    this.busInfo,
    this.freePlace,
    this.freePlaces,
    this.seatsSchema,
    this.withoutPd,
    this.withoutSelectPlaces,
    this.noticeInfo,
    this.insuranceInfo,
    this.commissionInfo,
  });

  factory TripDetail.fromRawJson(String str) => TripDetail.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory TripDetail.fromJson(Map<String, dynamic> json) => _$TripDetailFromJson(json);
  Map<String, dynamic> toJson() => _$TripDetailToJson(this);
}

@JsonSerializable()
class CommissionInfo {
  double? adult;
  double? child;
  double? baggage;

  CommissionInfo({
    this.adult,
    this.child,
    this.baggage,
  });

  factory CommissionInfo.fromJson(Map<String, dynamic> json) => _$CommissionInfoFromJson(json);
  Map<String, dynamic> toJson() => _$CommissionInfoToJson(this);
}

@JsonSerializable()
class InsuranceInfo {
  @JsonKey(name: "insurance_available")
  bool? insuranceAvailable;
  @JsonKey(name: "use_insurance_default")
  bool? useInsuranceDefault;
  @JsonKey(name: "price_insurer_adult")
  double? priceInsurerAdult;
  @JsonKey(name: "price_insurer_child")
  double? priceInsurerChild;
  @JsonKey(name: "insurer_rules")
  String? insurerRules;

  InsuranceInfo({
    this.insuranceAvailable,
    this.useInsuranceDefault,
    this.priceInsurerAdult,
    this.priceInsurerChild,
    this.insurerRules,
  });

  factory InsuranceInfo.fromJson(Map<String, dynamic> json) => _$InsuranceInfoFromJson(json);
  Map<String, dynamic> toJson() => _$InsuranceInfoToJson(this);
}

@JsonSerializable()
class NoticeInfo {
  @JsonKey(name: "notice_available")
  bool? noticeAvailable;
  @JsonKey(name: "use_notice_default")
  bool? useNoticeDefault;
  @JsonKey(name: "price_notice")
  double? priceNotice;

  NoticeInfo({
    this.noticeAvailable,
    this.useNoticeDefault,
    this.priceNotice,
  });

  factory NoticeInfo.fromJson(Map<String, dynamic> json) => _$NoticeInfoFromJson(json);
  Map<String, dynamic> toJson() => _$NoticeInfoToJson(this);
}

@JsonSerializable()
class SeatsSchema {
  int? number;
  Status? status;

  SeatsSchema({
    this.number,
    this.status,
  });

  factory SeatsSchema.fromJson(Map<String, dynamic> json) => _$SeatsSchemaFromJson(json);
  Map<String, dynamic> toJson() => _$SeatsSchemaToJson(this);
}

@JsonEnum(fieldRename: FieldRename.none)
enum Status {
  @JsonValue("busy")
  BUSY,

  @JsonValue("empty")
  EMPTY,

  @JsonValue("free")
  FREE,
}

final statusValues = EnumValues({"busy": Status.BUSY, "empty": Status.EMPTY, "free": Status.FREE});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}

//MARK: - PaymentButton
@JsonSerializable()
class PaymentButton {
  final int? id;
  final String? slug;

  PaymentButton({
    this.id,
    this.slug,
  });

  factory PaymentButton.fromJson(Map<String, dynamic> json) => _$PaymentButtonFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentButtonToJson(this);
}
