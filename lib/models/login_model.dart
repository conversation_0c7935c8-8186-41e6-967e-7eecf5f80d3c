import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'login_model.g.dart';

@JsonSerializable()
class TerminalData {
  String? refresh;
  String? access;
  String? host;
  String? error;

  TerminalData({
    this.refresh,
    this.access,
    this.host,
    this.error,
  });

  factory TerminalData.fromRawJson(String str) => TerminalData.fromJson(json.decode(str));
  String toRawJson() => json.encode(toJson());

  factory TerminalData.fromJson(Map<String, dynamic> json) => _$TerminalDataFromJson(json);
  Map<String, dynamic> toJson() => _$TerminalDataToJson(this);
}
