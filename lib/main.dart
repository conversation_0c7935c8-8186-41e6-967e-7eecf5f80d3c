import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:terminal/src/api.dart';
import 'package:terminal/src/globals.dart';
import 'package:terminal/ui/login/login_view.dart';
import 'package:terminal/ui/main_screen/main_screen.dart';
import 'package:terminal/ui/order_check/order_check_view.dart';
import 'package:terminal/ui/pass_info/pass_info_view.dart';
import 'package:terminal/ui/pass_manual/pass_birthdate_screen.dart';
import 'package:terminal/ui/pass_manual/pass_check_screen.dart';
import 'package:terminal/ui/pass_manual/pass_contacts_screen.dart';
import 'package:terminal/ui/pass_manual/pass_name_screen.dart';
import 'package:terminal/ui/pass_manual/pass_passport_screen.dart';
import 'package:terminal/ui/payment/error_payment/error_payment_logic.dart';
import 'package:terminal/ui/payment/error_payment/error_payment_screen.dart';
import 'package:terminal/ui/payment/payment/payment_logic.dart';
import 'package:terminal/ui/payment/return_info_payment/return_info_payment_screen.dart';
import 'package:terminal/ui/payment/return_process/return_process_screen.dart';
import 'package:terminal/ui/payment/return_ticket/return_ticket_screen.dart';
import 'package:terminal/ui/payment/return_ticket_choose/return_ticket_choose_screen.dart';
import 'package:terminal/ui/search_citizenship/search_view.dart';
import 'package:terminal/ui/support/support_screen.dart';
import 'package:terminal/ui/search_doc_type/search_view.dart';
import 'package:terminal/ui/search_from/search_from_view.dart';
import 'package:terminal/ui/search_to/search_to_view.dart';
import 'package:terminal/ui/select_date/select_date_view.dart';
import 'package:terminal/ui/select_route/select_route_view.dart';
import 'package:terminal/ui/select_trip_detail/select_trip_details_view.dart';
import 'package:terminal/ui/payment/payment/payment_screen.dart';
import 'package:terminal/ui/widgets/version_button.dart';
import 'package:timezone/data/latest.dart' as tz;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  Get.put<Globals>(Globals());
  Get.put<Api>(Api());

  await Get.find<Globals>().loadPrefs();

  tz.initializeTimeZones();

  //debugPaintSizeEnabled = true;
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  final globals = Get.find<Globals>();
  // final key = GlobalKey() ;

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      // key: key,

      debugShowCheckedModeBanner: false,
      // title: ' ',
      theme: ThemeData(
        visualDensity: VisualDensity.compact,
        fontFamily: "Overpass",
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      initialRoute: globals.isLoggedIn.value ? '/mainScreen' : '/loginScreen',
      getPages: [
        GetPage(name: '/loginScreen', page: () => LoginScreen()),
        GetPage(name: '/mainScreen', page: () => MainScreen()),
        GetPage(name: '/searchFromScreen', page: () => SearchFromScreen()),
        GetPage(name: '/searchToScreen', page: () => SearchToScreen()),
        GetPage(name: '/selectDateScreen', page: () => SelectDateScreen()),
        GetPage(name: '/selectRoutesScreen', page: () => SelectTripScreen()),
        GetPage(name: '/selectTripDetailsScreen', page: () => SelectTripDetailsScreen()),
        GetPage(name: '/passInfoScreen', page: () => PassInfoScreen()),
        GetPage(name: '/passportScreen', page: () => PassportScreen()),
        GetPage(name: '/passNameScreen', page: () => PassNameScreen()),
        GetPage(name: '/passBirthDateScreen', page: () => PassBirthDateScreen()),
        GetPage(name: '/passCheckScreen', page: () => PassCheckScreen()),
        GetPage(name: '/passContactsScreen', page: () => PassContactsScreen()),
        GetPage(name: '/orderCheckScreen', page: () => OrderCheckScreen()),
        GetPage(name: "/documentScreen", page: () => PassportScreen()),
        GetPage(name: '/citizenshipSearchScreen', page: () => SearchCitizenshipScreen()),
        GetPage(name: '/docTypeSearchScreen', page: () => SearchDocumentTypeScreen()),
        GetPage(name: '/paymentScreen', page: () => PaymentScreen()),
        GetPage(name: '/returnInfoPaymentCardScreen', page: () => ReturnInfoPaymentScreen(typePayment: PaymentMethod.card)),
        GetPage(name: '/returnInfoPaymentSpbScreen', page: () => ReturnInfoPaymentScreen(typePayment: PaymentMethod.spb)),
        GetPage(name: '/errorPaymentScreen', page: () => ErrorPaymentScreen(ErrorType.fail, "Ошибка")),
        GetPage(name: '/returnTicketScreen', page: () => ReturnTicketScreen()),
        GetPage(name: '/returnTicketChooseScreen', page: () => ReturnTicketChooseScreen()),
        GetPage(name: '/returnTicketProcessScreen', page: () => ReturnProcessScreen()),
        GetPage(name: '/supportScreen', page: () => SupportScreen()),
      ],
      //TODO: - Для тестов
      builder: (context, child) {
        return Overlay(
          initialEntries: [
            OverlayEntry(builder: (ctx) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                VersionButton.show(ctx);
              });
              return child!;
            }),
          ],
        );
      },
    );
  }
}
