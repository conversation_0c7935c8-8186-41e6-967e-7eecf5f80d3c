name: terminal
description: "A Terminal Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.21+1021

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  dio: ^5.8.0+1
  get: ^4.7.2
  rxdart: ^0.28.0
  json_annotation: 4.9.0

  shared_preferences: ^2.5.3
  flutter_launcher_icons: ^0.14.3
  punycode_converter: ^0.2.1
  dotted_border: ^2.1.0
#  table_calendar: ^3.1.3
  intl: ^0.20.2
  timezone: ^0.10.1
  pretty_qr_code: ^3.4.0
  mask_text_input_formatter: ^2.9.0
  
  #Скелетон
  shimmer: ^3.0.0

  #Логер
  talker: ^4.7.6
  talker_dio_logger: ^4.7.6
  talker_flutter: ^4.7.6

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.15
  json_serializable: ^6.9.5

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
#  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - lib/assets/images/
    - lib/assets/images/load.gif
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
     - family: Overpass
       fonts:
         - asset: lib/assets/fonts/Overpass-Thin.ttf
           weight: 100
         - asset: lib/assets/fonts/Overpass-ExtraLight.ttf
           weight: 200
         - asset: lib/assets/fonts/Overpass-Light.ttf
           weight: 300
         - asset: lib/assets/fonts/Overpass-Regular.ttf
           weight: 400
         - asset: lib/assets/fonts/Overpass-Medium.ttf
           weight: 500
         - asset: lib/assets/fonts/Overpass-SemiBold.ttf
           weight: 600
         - asset: lib/assets/fonts/Overpass-Black.ttf
           weight: 900
         - asset: lib/assets/fonts/Overpass-ExtraBold.ttf
           weight: 800
         - asset: lib/assets/fonts/Overpass-Bold.ttf
           weight: 700
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
