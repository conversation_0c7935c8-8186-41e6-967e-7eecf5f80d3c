FROM ubuntu:20.04

# Обновляем пакеты и устанавливаем зависимости
RUN apt-get update && apt-get install -y \
    curl \
    git \
    unzip \
    xz-utils \
    libglu1-mesa \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Загружаем и распаковываем Flutter
RUN curl -o flutter.tar.xz https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.24.5-stable.tar.xz && \
    tar xf flutter.tar.xz && \
    rm flutter.tar.xz

# Добавляем Flutter в PATH
ENV PATH="/flutter/bin:${PATH}"

# Проверяем версию Flutter
RUN flutter --version
