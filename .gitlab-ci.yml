stages:
  - build
  - deploy

build:
  stage: build
  image: cloudb.gitlab.yandexcloud.net:5050/easyway/terminal:latest
  script:
    - echo "=== Начало сборки ==="
    # Добавляем Flutter как безопасный каталог
    - git config --global --add safe.directory /flutter
    # Проверяем и обновляем Flutter
    - echo "=== Обновляем Flutter ==="
    - flutter channel stable
    - flutter upgrade
    - flutter --version
    # Устанавливаем зависимости
    - echo "=== Устанавливаем зависимости ==="
    - flutter pub get
    # Сборка для веба
    - echo "=== Собираем проект ==="
    - flutter build web --release
    - echo "=== Проверяем содержимое build/web ==="
    - ls -l build/web
  artifacts:
    paths:
      - build/web
  only:
    - release


deploy:
  stage: deploy
  image: alpine:latest
  before_script:
    - echo "=== Устанавливаем необходимые инструменты ==="
    - apk add --no-cache openssh
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config
  script:
    # Копируем файлы на сервер
    - echo "=== Копируем файлы на сервер ==="
    - scp -P 22 -r build/web/* $SERVER_USER@$SERVER_IP:/home/<USER>/front-deploy/volumes/terminal/
    - echo "=== Проверяем содержимое на сервере ==="
    - ssh $SERVER_USER@$SERVER_IP -p 22 "ls -l /home/<USER>/front-deploy/volumes/terminal"
    # Запускаем скрипт деплоя
    - echo "=== Запускаем скрипт деплоя ==="
    - ssh $SERVER_USER@$SERVER_IP -p 22 "cd /home/<USER>/front-deploy && docker restart frontend_nginx"
  only:
    - release

